/* Base styles */
body {
  font-family: "Inter", sans-serif;
  margin: 0;
  padding: 0;
  display: flex;
  background: #f0f2f5;
  color: #1f2937;
}

/* Sidebar */
.sidebar {
  width: 200px;
  background: linear-gradient(180deg, #1e3a8a, #3b82f6);
  color: white;
  height: 100vh;
  padding: 25px 20px;
  position: fixed;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.08);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-link {
  text-decoration: none;
  color: whitesmoke;
}

.sidebar h2 {
  font-size: 24px;
  margin-bottom: 30px;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.sidebar li {
  margin: 18px 0;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 10px 12px;
  border-radius: 8px;
  transition: background 0.3s;
}

.sidebar li:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #facc15;
}

.sidebar li i {
  font-size: 18px;
}

.sidebar li a {
  text-decoration: none;
  color: white;
}

.sidebar .bottom-img {
  margin-top: 20px;
  padding: 10px;
  text-align: center;
}

.sidebar .bottom-img img {
  width: 110px;
  opacity: 0.95;
  border-radius: 12px;
  background: white;
  padding: 6px;
}

/* Main */
.main {
  margin-left: 240px;
  padding: 40px 30px;
  flex: 1;
}

.main h1 {
  font-size: 28px;
  margin-bottom: 10px;
  color: #1e3a8a;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  margin-bottom: 30px;
}

.welcome-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  padding: 15px 20px;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  margin-top: 10px;
}

.driver-name {
  font-weight: bold;
  color: #1e3a8a;
}

.date-time {
  color: #6b7280;
  font-size: 14px;
}

/* Section styles */
.section {
  margin-bottom: 40px;
}

.section h2 {
  font-size: 20px;
  color: #1e3a8a;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e5e7eb;
  position: relative;
}

.section h2::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: #3b82f6;
}

/* Card grid */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.card {
  background-color: #ffffff;
  padding: 25px 20px;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
  transition: transform 0.2s ease;
}

.card:hover {
  transform: translateY(-5px);
}

.card h3 {
  margin-bottom: 10px;
  font-size: 18px;
  color: #1e3a8a;
}

.stat {
  font-size: 28px;
  font-weight: bold;
  color: #1e3a8a;
  margin: 15px 0 10px;
}

.stat-desc {
  color: #6b7280;
  font-size: 14px;
}

/* Toggle */
.toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-top: 10px;
}

.toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  border-radius: 24px;
  transition: 0.4s;
}

.slider:before {
  content: "";
  position: absolute;
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

input:checked + .slider {
  background-color: #22c55e;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Table */
.table-container {
  overflow-x: auto;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 15px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

th,
td {
  text-align: left;
  padding: 14px 16px;
  border-bottom: 1px solid #e5e7eb;
}

th {
  background-color: #f1f5f9;
  color: #1e3a8a;
}

/* Buttons */
.accept-btn {
  background-color: #10b981;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 5px;
  cursor: pointer;
  margin-right: 5px;
  transition: background-color 0.3s ease;
}

.accept-btn:hover {
  background-color: #059669;
}

.reject-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.reject-btn:hover {
  background-color: #dc2626;
}

/* Ride cards */
.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.ride-card {
  background: white;
  padding: 15px;
  border-radius: 10px;
  flex: 1 1 220px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.ride-card:hover {
  transform: translateY(-4px);
}

/* No rides message */
.no-rides {
  background-color: #f9fafb;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  color: #6b7280;
  font-size: 16px;
  margin: 20px 0;
  border: 1px dashed #e5e7eb;
  width: 100%;
}

/* Responsive */
@media (max-width: 768px) {
  .main {
    margin-left: 0;
    padding: 25px;
    width: 100%;
  }

  .sidebar {
    display: none;
  }

  .card-grid {
    grid-template-columns: 1fr;
  }

  .card-list {
    flex-direction: column;
  }
}
