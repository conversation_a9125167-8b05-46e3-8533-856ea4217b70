// English (en)
plupload.addI18n({"%d files queued":"%d files queued","%s already present in the queue.":"%s already present in the queue.","%s specified, but cannot be found.":"%s specified, but cannot be found.","Add Files":"Add Files","Add files to the upload queue and click the start button.":"Add files to the upload queue and click the start button.","b":"b","Close":"Close","Drag files here.":"Drag files here.","Duplicate file error.":"Duplicate file error.","Error: File too large:":"Error: File too large:","Error: Invalid file extension:":"Error: Invalid file extension:","File count error.":"File count error.","File extension error.":"File extension error.","File size error.":"File size error.","File: %s":"File: %s","File: %s, size: %d, max file size: %d":"File: %s, size: %d, max file size: %d","Filename":"Filename","gb":"gb","HTTP Error.":"HTTP Error.","Image format either wrong or not supported.":"Image format either wrong or not supported.","Init error.":"Init error.","kb":"kb","List":"List","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Select files","Size":"Size","Start Upload":"Start Upload","Status":"Status","Stop Upload":"Stop Upload","tb":"tb","Thumbnails":"Thumbnails","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","Uploaded %d/%d files":"Uploaded %d/%d files","You must specify either browse_button or drop_element.":"You must specify either browse_button or drop_element."});