<?php
session_start();

// Check if driver is logged in
if (!isset($_SESSION['driver_logged_in']) || $_SESSION['driver_logged_in'] !== true) {
    header("Location: Driver-login.html");
    exit();
}

// Include database connection
require_once 'panel-admin-main1/db.php';

// Get driver info
$driverName = $_SESSION['driver_name'] ?? 'Driver';
$driverEmail = $_SESSION['driver_email'] ?? '';
$driverId = $_SESSION['driver_id'] ?? 0;

// Get stats for the driver
$todayStart = date('Y-m-d 00:00:00');
$todayEnd = date('Y-m-d 23:59:59');
$monthStart = date('Y-m-01 00:00:00');
$monthEnd = date('Y-m-t 23:59:59');

// Count pending deliveries for today
$pendingStmt = $conn->prepare("SELECT COUNT(*) as count FROM parcel_requests WHERE status = 'pending' AND created_at BETWEEN ? AND ?");
$pendingStmt->bind_param("ss", $todayStart, $todayEnd);
$pendingStmt->execute();
$pendingResult = $pendingStmt->get_result();
$pendingCount = $pendingResult->fetch_assoc()['count'] ?? 0;

// Count completed deliveries
$completedStmt = $conn->prepare("SELECT COUNT(*) as count FROM parcel_requests WHERE assigned_driver_id = ? AND status = 'completed'");
$completedStmt->bind_param("i", $driverId);
$completedStmt->execute();
$completedResult = $completedStmt->get_result();
$completedCount = $completedResult->fetch_assoc()['count'] ?? 0;

// Calculate earnings this month
$earningsStmt = $conn->prepare("SELECT SUM(price) as total FROM parcel_requests WHERE assigned_driver_id = ? AND status IN ('accepted', 'completed') AND created_at BETWEEN ? AND ?");
$earningsStmt->bind_param("iss", $driverId, $monthStart, $monthEnd);
$earningsStmt->execute();
$earningsResult = $earningsStmt->get_result();
$earnings = $earningsResult->fetch_assoc()['total'] ?? 0;

// Format earnings
$formattedEarnings = number_format($earnings, 2);
?>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Dashboard</title>
    <link rel="stylesheet" href="Page-2.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
  </head>
  <body>
    <div class="sidebar">
      <div>
        <h2>
          <a href="driver-dashboard.php" class="logo-link">Driver Panel</a>
        </h2>
        <ul>
            <li>
              <a href="driver-dashboard.php" class="section1">
                <i class="fas fa-home"></i> Dashboard
              </a>
            </li>
            <li>
              <a href="myrides.php" class="section1">
                <i class="fas fa-car-side"></i> My Rides
              </a>
            </li>
            <li>
              <a href="driver-logout.php" class="section1">
                <i class="fas fa-sign-out-alt"></i> Log Out
              </a>
            </li>
          </ul>
      </div>

      <div class="bottom-img">
        <img src="parcel-box.png" alt="Parcel Icon" />
      </div>
    </div>

    <div class="main">
      <!-- Hidden input for driver ID -->
      <input type="hidden" id="driver-id" value="<?php echo isset($_SESSION['driver_id']) ? htmlspecialchars($_SESSION['driver_id']) : ''; ?>">
      <div class="welcome-header">
        <h1>Driver Dashboard</h1>
        <div class="welcome-message">
          <p>Welcome, <span class="driver-name"><?php echo htmlspecialchars($driverName); ?></span>!</p>
          <p class="date-time"><?php echo date('l, F j, Y'); ?></p>
        </div>
      </div>

      <!-- Dashboard Stats -->
      <div class="card-grid">
        <div class="card">
          <h3>Today's Deliveries</h3>
          <p class="stat"><?php echo $pendingCount; ?></p>
          <p class="stat-desc">Pending deliveries for today</p>
        </div>

        <div class="card">
          <h3>Completed Rides</h3>
          <p class="stat"><?php echo $completedCount; ?></p>
          <p class="stat-desc">Total completed deliveries</p>
        </div>

        <div class="card">
          <h3>Earnings</h3>
          <p class="stat">₹<?php echo $formattedEarnings; ?></p>
          <p class="stat-desc">Total earnings this month</p>
        </div>

        <div class="card">
          <h3>Rating</h3>
          <p class="stat">4.8 <i class="fas fa-star" style="color: #facc15;"></i></p>
          <p class="stat-desc">Your current rating</p>
        </div>
      </div>

      <!-- Recent Activity -->
      <div class="section">
        <h2>Recent Activity</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Date</th>
                <th>Activity</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              <?php
              // Fetch recent activity for this driver
              $activitySql = "SELECT id, item_description, status, created_at FROM parcel_requests
                              WHERE assigned_driver_id = ? AND status IN ('accepted', 'completed')
                              ORDER BY created_at DESC LIMIT 5";
              $activityStmt = $conn->prepare($activitySql);
              $activityStmt->bind_param("i", $driverId);
              $activityStmt->execute();
              $activityResult = $activityStmt->get_result();

              if ($activityResult && $activityResult->num_rows > 0) {
                while ($row = $activityResult->fetch_assoc()) {
                  $date = date('M d, Y', strtotime($row['created_at']));
                  $statusColor = $row['status'] === 'completed' ? '#10b981' : '#3b82f6';
                  $statusText = ucfirst($row['status']);
                  $activity = $row['status'] === 'completed' ? 'Delivered' : 'Accepted';

                  echo "<tr>";
                  echo "<td>{$date}</td>";
                  echo "<td>{$activity} package #{$row['id']} - {$row['item_description']}</td>";
                  echo "<td><span style='color: {$statusColor};'>{$statusText}</span></td>";
                  echo "</tr>";
                }
              } else {
                echo "<tr><td colspan='3' style='text-align: center;'>No recent activity</td></tr>";
              }
              ?>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <script>
      // Add any dashboard-specific JavaScript here
    </script>
  </body>
</html>
