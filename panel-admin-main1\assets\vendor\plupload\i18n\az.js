// Azerbaijani (az)
plupload.addI18n({"%d files queued":"Növbədə %d fayl var","%s already present in the queue.":"%s artıq növbədə var.","%s specified, but cannot be found.":"","Add Files":"Fayl əlavə et","Add files to the upload queue and click the start button.":"Faylları əlavə edin və yüklə düyməsinə klikləyin.","b":"b","Close":"Bağla","Drag files here.":"<PERSON>lları bura çəkin.","Duplicate file error.":"Bu fayl artıq növbədə var.","Error: File too large:":"Xəta:Fayl həcmi çox böyükdür.","Error: Invalid file extension:":"Xəta: Yanlış fayl uzantısı:","File count error.":"Fayl sayı çox böyükdür.","File extension error.":"<PERSON>l uzantısı xətası.","File size error.":"Fayl həcmi xətası.","File: %s":"Fayl: %s","File: %s, size: %d, max file size: %d":"Fayl: %s, həcm: %d, max fayl həcmi: %d","Filename":"Faylın adı","gb":"gb","HTTP Error.":"HTTP xətası.","Image format either wrong or not supported.":"Şəklin formatı uyğun deyil və ya dəstəklənmir.","Init error.":"Init error.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Faylları seçin","Size":"Həcm","Start Upload":"Yüklə","Status":"Status","Stop Upload":"Yükləməni saxla","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"Yükləmə ünvanı səhvdir və ya mövcud deyil","Uploaded %d/%d files":"%d/%d fayl yüklənib","You must specify either browse_button or drop_element.":""});