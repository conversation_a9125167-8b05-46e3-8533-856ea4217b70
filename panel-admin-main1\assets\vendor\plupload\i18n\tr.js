// Turkish (tr)
plupload.addI18n({"%d files queued":"Kuyrukta %d dosya var.","%s already present in the queue.":"%s kuyrukta zaten mevcut.","%s specified, but cannot be found.":"%s tanımlandı fakat bulunamadı.","Add Files":"<PERSON>sy<PERSON> ekle","Add files to the upload queue and click the start button.":"Dosyaları kuyruğa ekleyin ve başlatma butonuna tıklayın.","b":"bayt","Close":"Kapat","Drag files here.":"Dosyaları buraya bırakın.","Duplicate file error.":"Yinelenen dosya hatası.","Error: File too large:":"Hata: Dosya çok büyük:","Error: Invalid file extension:":"Hata: Geçersiz dosya uzantısı:","File count error.":"Dosya sayım hatası.","File extension error.":"Dosya uzantısı hatası.","File size error.":"Dosya boyutu hatası.","File: %s":"Dosya: %s","File: %s, size: %d, max file size: %d":"Dosya: %s, boyut: %d, maksimum dosya boyutu: %d","Filename":"Dosya adı","gb":"gb","HTTP Error.":"HTTP hatası.","Image format either wrong or not supported.":"Resim formatı yanlış ya da desteklenmiyor.","Init error.":"Başlangıç hatası.","kb":"kb","List":"Liste","mb":"mb","N/A":"-","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Çözünürlük sınırların dışındadır! <b>%s</b> modu en fazla %wx%hpx desteklemektedir.","Runtime ran out of available memory.":"İşlem için yeterli bellek yok.","Select files":"Dosyaları seç","Size":"Boyut","Start Upload":"Yüklemeyi başlat","Status":"Durum","Stop Upload":"Yüklemeyi durdur","tb":"tb","Thumbnails":"Önizlemeler","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Yükleme elemanı aynı anda %d dosya kabul eder. Ekstra dosyalar işleme konulmaz.","Upload URL might be wrong or doesn't exist.":"URL yok ya da hatalı olabilir.","Uploaded %d/%d files":"%d/%d dosya yüklendi","You must specify either browse_button or drop_element.":"browse_button veya drop_element değişkenlerini belirlemelisiniz."});