<?php
session_start();

// Check if driver is logged in
if (!isset($_SESSION['driver_id'])) {
    header("Location: Driver-login.html");
    exit();
}

$driver_id = $_SESSION['driver_id'];
$driver_name = $_SESSION['driver_name'] ?? 'Driver';
?>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Driver Dashboard</title>
    <link rel="stylesheet" href="Page-2.css" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter&display=swap"
      rel="stylesheet"
    />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />

  </head>
  <body>
    <div class="sidebar">
      <div>
        <h2>
          <a href="index.html" class="logo-link">Driver Panel</a>
        </h2>

        <ul>
            <li>
              <a href="index.html" class="section1">
                <i class="fas fa-home"></i> Dashboard
              </a>
            </li>
            <li>
              <a href="myrides.php" class="section1">
                <i class="fas fa-car-side"></i> My Rides
              </a>
            </li>
            <li>
              <a href="driver-logout.php" class="section1">
                <i class="fas fa-sign-out-alt"></i> Log Out
              </a>
            </li>
          </ul>
      </div>

      <div class="bottom-img">
        <img src="parcel-box.png" alt="Parcel Icon" />
      </div>
    </div>

    <div class="main">
      <h1>Welcome, <?php echo htmlspecialchars($driver_name); ?>!</h1>

      <div class="card-grid">
        <div class="card">
          <h3>Earnings Overview</h3>
          <p>₹12,350</p>
        </div>
        <div class="card">
          <h3>Ride Availability</h3>
          <label class="toggle">
            <input type="checkbox" id="availabilityToggle" checked />
            <span class="slider"></span>
          </label>
          <p id="availabilityStatus">Currently Online</p>
        </div>
        <div class="card">
          <h3>Total Rides</h3>
          <p>28</p>
        </div>
        <div class="card">
          <h3>Avg. Rating</h3>
          <p>4.8 ★</p>
        </div>
      </div>
    </div>

    <script src="page-2.js"></script>
    <script>
      // Toggle availability status
      const toggle = document.getElementById('availabilityToggle');
      const statusText = document.getElementById('availabilityStatus');

      toggle.addEventListener('change', function() {
        statusText.textContent = this.checked ? 'Currently Online' : 'Currently Offline';

        // Update availability in database
        fetch('update_availability.php', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: 'availability=' + (this.checked ? 1 : 0)
        })
        .then(response => response.text())
        .then(data => console.log(data));
      });
    </script>
  </body>
</html>
