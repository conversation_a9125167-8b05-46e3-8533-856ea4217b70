<?php
// Database connection
require_once 'panel-admin-main1/db.php';

// Set headers to handle AJAX requests
header('Content-Type: application/json');

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get form data
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    // Try to get regular POST data if JSON parsing failed
    $data = $_POST;
}

// Validate required fields
$required_fields = ['pickup', 'dropoff', 'item_description'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        echo json_encode(['success' => false, 'message' => 'Missing required field: ' . $field]);
        exit;
    }
}

// Extract data
$pickup_location = $data['pickup'];
$dropoff_location = $data['dropoff'];
$item_description = $data['item_description'];
$parcel_type = $data['parcel_type'] ?? 'Not specified';
$vehicle_type = $data['vehicle_type'] ?? 'Not specified';
$customer_name = $data['customer_name'] ?? 'Guest';
$customer_phone = $data['customer_phone'] ?? '';
$customer_email = $data['customer_email'] ?? '';

// Calculate a simple price based on parcel and vehicle type
// In a real application, you would have a more sophisticated pricing algorithm
$base_price = 100; // Base price in rupees
$vehicle_multiplier = 1.0;
switch (strtolower($vehicle_type)) {
    case 'bike':
        $vehicle_multiplier = 1.0;
        break;
    case 'car':
        $vehicle_multiplier = 1.5;
        break;
    case 'van':
        $vehicle_multiplier = 2.0;
        break;
    case 'truck':
        $vehicle_multiplier = 3.0;
        break;
}
$price = $base_price * $vehicle_multiplier;

// Prepare and execute SQL statement
try {
    $stmt = $conn->prepare("INSERT INTO parcel_requests (pickup_location, dropoff_location, item_description, parcel_type, vehicle_type, customer_name, customer_phone, customer_email, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $stmt->bind_param("ssssssssd", 
        $pickup_location, 
        $dropoff_location, 
        $item_description, 
        $parcel_type, 
        $vehicle_type, 
        $customer_name, 
        $customer_phone, 
        $customer_email, 
        $price
    );
    
    $result = $stmt->execute();
    
    if ($result) {
        echo json_encode([
            'success' => true, 
            'message' => 'Parcel request saved successfully',
            'request_id' => $conn->insert_id,
            'price' => $price
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save request: ' . $stmt->error]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
