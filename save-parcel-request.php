<?php
// Database connection
require_once 'panel-admin-main1/db.php';

// Set headers to handle AJAX requests
header('Content-Type: application/json');

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get form data
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    // Try to get regular POST data if JSON parsing failed
    $data = $_POST;
}

// Validate required fields
$required_fields = ['pickup', 'dropoff', 'item_description'];
foreach ($required_fields as $field) {
    if (empty($data[$field])) {
        echo json_encode(['success' => false, 'message' => 'Missing required field: ' . $field]);
        exit;
    }
}

// Extract data
$pickup_location = $data['pickup'];
$dropoff_location = $data['dropoff'];
$item_description = $data['item_description'];
$parcel_type = $data['parcel_type'] ?? 'Not specified';
$service_type = $data['service_type'] ?? 'Not specified';
$service_id = $data['service_id'] ?? '';
$customer_name = $data['customer_name'] ?? 'Guest';
$customer_phone = $data['customer_phone'] ?? '';
$customer_email = $data['customer_email'] ?? '';

// Calculate price based on service type and ID
$base_price = 100; // Default base price in rupees
$price = $base_price;

// If we have a service ID, get the base fare from the services table
if (!empty($service_id)) {
    try {
        $service_query = "SELECT base_fare FROM services WHERE id = ?";
        $stmt_service = $conn->prepare($service_query);
        $stmt_service->bind_param("i", $service_id);
        $stmt_service->execute();
        $result = $stmt_service->get_result();

        if ($row = $result->fetch_assoc()) {
            $service_price = floatval($row['base_fare']);
            $price += $service_price;
        }

        $stmt_service->close();
    } catch (Exception $e) {
        // If there's an error, just use the default price
        error_log("Error fetching service price: " . $e->getMessage());
    }
} else {
    // Fallback to the old calculation if no service_id is provided
    $service_multiplier = 1.0;
    switch (strtolower($service_type)) {
        case 'bike':
            $service_multiplier = 1.0;
            break;
        case 'car':
            $service_multiplier = 1.5;
            break;
        case 'van':
            $service_multiplier = 2.0;
            break;
        case 'truck':
            $service_multiplier = 3.0;
            break;
    }
    $price = $base_price * $service_multiplier;
}

// Prepare and execute SQL statement
try {
    // Check if the parcel_requests table has the service_type and service_id columns
    $check_columns_query = "SHOW COLUMNS FROM parcel_requests LIKE 'service_type'";
    $result = $conn->query($check_columns_query);

    if ($result->num_rows == 0) {
        // Add the new columns if they don't exist
        $conn->query("ALTER TABLE parcel_requests ADD COLUMN service_type VARCHAR(100) NOT NULL AFTER parcel_type");
        $conn->query("ALTER TABLE parcel_requests ADD COLUMN service_id VARCHAR(20) NOT NULL AFTER service_type");

        // Rename vehicle_type column to service_type for existing records
        $conn->query("ALTER TABLE parcel_requests CHANGE COLUMN vehicle_type service_type VARCHAR(100) NOT NULL");
    }

    $stmt = $conn->prepare("INSERT INTO parcel_requests (pickup_location, dropoff_location, item_description, parcel_type, service_type, service_id, customer_name, customer_phone, customer_email, price) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $stmt->bind_param("sssssssssd",
        $pickup_location,
        $dropoff_location,
        $item_description,
        $parcel_type,
        $service_type,
        $service_id,
        $customer_name,
        $customer_phone,
        $customer_email,
        $price
    );

    $result = $stmt->execute();

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Parcel request saved successfully',
            'request_id' => $conn->insert_id,
            'price' => $price
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to save request: ' . $stmt->error]);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
