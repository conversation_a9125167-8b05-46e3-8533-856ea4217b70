@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

#profile-management {
  background: #f8f9fa;
  padding: 40px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin: auto;
}
#profile-management h2 {
  text-align: center;
  font-family: 'Roboto', sans-serif;
  font-size: 28px;
  font-weight: 700;
  color: #007bff;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  margin-bottom: 20px;
  padding: 10px;
  background: linear-gradient(90deg, #007bff, #0056b3);
  color: white;
  border-radius: 8px;
}
.u-form {
  display: flex;
  flex-direction: column;
}
.u-form label {
  font-weight: bold;
  margin-top: 10px;
}
.u-form input, .u-form select {
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  margin-top: 5px;
}
.u-form button {
  margin-top: 20px;
  background-color: #007bff;
  color: white;
  padding: 10px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
}
.u-form button:hover {
  background-color: #0056b3;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ccc0c0; 
  padding: 10px 20px;
  font-size: 14px;
}
.social-media a {
  margin-left: 15px;
  text-decoration: none;
  color: rgb(0, 0, 0);
  font-size: 18px;
  transition: color 0.3s ease-in-out;
}
.u-logo-image-1 {
  max-height: 50px; /* Adjust logo size */

}
.social-media a:hover {
  color: lightblue;
}

footer {
  background-color: #f8f9fc;
  padding: 40px 20px;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: auto;
}

.footer-section {
  width: 22%;
  min-width: 250px;
  margin-bottom: 20px;
}

.footer-section h4 {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
  color: black;
}

.footer-section p, 
.footer-section ul li a {
  font-size: 14px;
  color: black;
  transition: 0.3s;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a:hover {
  color: #007bff;
}

.company-info img {
  width: 50px;
  margin-bottom: 10px;
}

.company-info h3 {
  font-size: 18px;
  color: black;
}

.company-info p {
  font-size: 13px;
  color: black;
}

.social-icons {
  margin-top: 10px;
}

.social-icons a {
  font-size: 18px;
  color: #007bff;
  margin-right: 12px;
  transition: 0.3s;
  display: inline-block;
}

.social-icons a:hover {
  color: black;
  transform: scale(1.1);
}

.contact p {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.contact i {
  color: #007bff;
  font-size: 16px;
}

.footer-bottom {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #ddd;
  margin-top: 20px;
}

.footer-bottom p {
  font-size: 13px;
  color: black;
}

.policy-links a {
  text-decoration: none;
  color: #007bff;
  font-size: 13px;
}

.policy-links a:hover {
  text-decoration: underline;
  color: black;
}
