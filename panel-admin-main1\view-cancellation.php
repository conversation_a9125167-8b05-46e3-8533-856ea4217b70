<?php 
require_once __DIR__ . '/header.php'; 
require_once __DIR__ . "/db.php"; 

$sql = "SELECT * FROM cancellations ORDER BY date DESC";
$result = $conn->query($sql);
?>

<div class="content container-fluid">
    <div class="page-header">
        <h1 class="page-header-title"><?= translate('view_cancellations') ?></h1>
    </div>
    
    <div class="row g-3">
        <div class="col-md-3">
            <div class="input-group input-group-sm">
                <div class="input-group-text"><i class="bi-search"></i></div>
                <input type="search" id="searchInput" class="form-control" placeholder="<?= translate('search_here') ?>">
            </div>
        </div>
        <div class="col-md-6 offset-md-3 text-end">
            <button class="btn btn-sm btn-secondary" id="filterButton" data-bs-toggle="modal" data-bs-target="#filterModal">
                <?= translate('filter') ?> <i class="bi bi-funnel-fill"></i>
            </button>
            <!-- Filter Modal -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filter</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="filterForm">
                    <div class="row">
                        <div class="col-md-6 text-start">
                            <label class="form-label fw-semibold">Filter by Customer Name</label>
                            <input type="text" id="filterCustomerName" class="form-control" placeholder="Enter name">
                        </div>
                        <div class="col-md-6 text-start">
                            <label class="form-label fw-semibold">Filter by Cancellation Reason</label>
                            <input type="text" id="filterCancelReason" class="form-control" placeholder="Enter reason">
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6 text-start">
                            <label class="form-label fw-semibold">Filter by Customer ID</label>
                            <input type="text" id="filterCustomerId" class="form-control" placeholder="Enter Customer ID">
                        </div>
                        <div class="col-md-6 text-start">
                            <label class="form-label fw-semibold">Filter by Vehicle ID</label>
                            <input type="text" id="filterVehicleId" class="form-control" placeholder="Enter Vehicle ID">
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button type="button" class="btn btn-outline-danger" id="clearFilters">Clear all filters</button>
                        <div>
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" id="applyFilters">Apply Filters</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.getElementById("applyFilters").addEventListener("click", function () {
        let customerName = document.getElementById("filterCustomerName").value.toLowerCase();
        let cancelReason = document.getElementById("filterCancelReason").value.toLowerCase();
        let customerId = document.getElementById("filterCustomerId").value.toLowerCase();
        let vehicleId = document.getElementById("filterVehicleId").value.toLowerCase();

        document.querySelectorAll("#dataTableBody tr").forEach(row => {
            let name = row.children[1].innerText.toLowerCase();
            let reason = row.children[2].innerText.toLowerCase();
            let id = row.children[3].innerText.toLowerCase();
            let vehicle = row.children[4].innerText.toLowerCase();

            let matches = (!customerName || name.includes(customerName)) &&
                          (!cancelReason || reason.includes(cancelReason)) &&
                          (!customerId || id.includes(customerId)) &&
                          (!vehicleId || vehicle.includes(vehicleId));

            row.style.display = matches ? "" : "none";
        });

        new bootstrap.Modal(document.getElementById("filterModal")).hide();
    });

    document.getElementById("clearFilters").addEventListener("click", function () {
        document.getElementById("filterForm").reset();
        document.querySelectorAll("#dataTableBody tr").forEach(row => row.style.display = "");
    });
</script>

        </div>
    </div>
    
    <div class="table-responsive mt-3">
        <table id="data-table" class="table table-bordered table-nowrap table-align-middle">
            <thead class="thead-light">
                <tr>
                    <th>ID</th>
                    <th><?= translate('customer_name') ?></th>
                    <th><?= translate('cancellation_reason') ?></th>
                    <th><?= translate('customer_id') ?></th>
                    <th><?= translate('vehicle_id') ?></th>
                    <th><?= translate('driver_name') ?></th>
                    <th><?= translate('date') ?></th>
                    <th><?= translate('action') ?></th>
                </tr>
            </thead>
            <tbody id="dataTableBody">
            <?php while ($row = $result->fetch_assoc()) : ?>
                <tr>
                    <td><?= $row['id'] ?></td>
                    <td class="searchable"> <?= $row['customer_name'] ?> </td>
                    <td class="searchable"> <?= $row['cancellation_reason'] ?> </td>
                    <td class="searchable"> <?= $row['customer_id'] ?> </td>
                    <td class="searchable"> <?= $row['vehicle_id'] ?> </td>
                    <td class="searchable"> <?= $row['driver_name'] ?> </td>
                    <td><?= $row['date'] ?></td>
                    <td>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-gray dropdown-toggle action-btn" type="button" data-bs-toggle="dropdown">
                                <?= translate('action') ?>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <button class="dropdown-item action-option edit-btn"
                                        data-id="<?= $row['id'] ?>"
                                        data-customer_name="<?= $row['customer_name'] ?>"
                                        data-cancel_reason="<?= $row['cancellation_reason'] ?>"
                                        data-customer_id="<?= $row['customer_id'] ?>"
                                        data-vehicle_id="<?= $row['vehicle_id'] ?>"
                                        data-driver_name="<?= $row['driver_name'] ?>"
                                        data-additional_info="<?= $row['additional_info'] ?>"
                                        data-bs-toggle="modal" data-bs-target="#editModal">
                                        <?= translate('edit') ?>
                                    </button>
                                </li>
                                <li>
                                    <a class="dropdown-item text-danger" href="delete-cancellation.php?id=<?= $row['id'] ?>" onclick="return confirm('<?= translate('are_you_sure') ?>');">
                                        <?= translate('delete') ?>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </td>
                </tr>
            <?php endwhile; ?>
            </tbody>        
        </table>
    </div>
</div>

<?php require_once __DIR__ . '/footer.php'; ?>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Cancellation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editForm" method="POST" action="update-cancellation.php">
                    <input type="hidden" name="id" id="editId">
                    
                    <label>Customer Name</label>
                    <input type="text" name="customer_name" id="editCustomerName" required class="form-control">

                    <label>Cancellation Reason</label>
                    <input type="text" name="cancel_reason" id="editCancelReason" required class="form-control">

                    <label>Customer ID</label>
                    <input type="text" name="customer_id" id="editCustomerId" required class="form-control">

                    <label>Vehicle ID</label>
                    <input type="text" name="vehicle_id" id="editVehicleId" class="form-control">

                    <label>Driver Name</label>
                    <input type="text" name="driver_name" id="editDriverName" class="form-control">

                    <label>Additional Info</label>
                    <textarea name="additional_info" id="editAdditionalInfo" class="form-control"></textarea>

                    <button type="submit" class="btn btn-primary mt-3">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>



<script>
document.getElementById("searchInput").addEventListener("input", function () {
    let filter = this.value.toLowerCase();
    document.querySelectorAll("#dataTableBody tr").forEach(row => {
        row.style.display = row.innerText.toLowerCase().includes(filter) ? "" : "none";
    });
});

document.querySelectorAll(".edit-btn").forEach(btn => {
    btn.addEventListener("click", function() {
        document.getElementById("editId").value = this.dataset.id;
        document.getElementById("editCustomerName").value = this.dataset.customer_name;
        document.getElementById("editCancelReason").value = this.dataset.cancel_reason;
        document.getElementById("editCustomerId").value = this.dataset.customer_id;
        document.getElementById("editVehicleId").value = this.dataset.vehicle_id;
        document.getElementById("editDriverName").value = this.dataset.driver_name;
        document.getElementById("editAdditionalInfo").value = this.dataset.additional_info;

        
    });
});
</script>
