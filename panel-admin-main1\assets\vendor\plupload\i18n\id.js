// Indonesian (id)
plupload.addI18n({"%d files queued":"%d file dalam antrian","%s already present in the queue.":"%s sudah ada dalam daftar antrian","%s specified, but cannot be found.":"","Add Files":"Tambah File","Add files to the upload queue and click the start button.":"Tambahkan file kedalam antrian upload dan klik tombol Mulai","b":"b","Close":"Tutup","Drag files here.":"Tarik file kesini","Duplicate file error.":"Terjadi duplikasi file","Error: File too large:":"Kesalahan: File terlalu besar","Error: Invalid file extension:":"Kesalahan: Ekstensi file tidak dikenal","File count error.":"Kesalahan pada jumlah file","File extension error.":"Kesalahan pada ekstensi file","File size error.":"Kesalahan pada ukuran file","File: %s":"File: %s","File: %s, size: %d, max file size: %d":"File: %s, ukuran: %d, maksimum ukuran file: %d","Filename":"Nama File","gb":"gb","HTTP Error.":"HTTP Bermasalah","Image format either wrong or not supported.":"Kesalahan pada jenis gambar atau jenis file tidak didukung","Init error.":"Kesalahan pada Init","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Tidak cukup memori","Select files":"Pilih file","Size":"Ukuran","Start Upload":"Mulai Upload","Status":"Status","Stop Upload":"Hentikan Upload","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Tempat untuk upload hanya menerima %d file(s) dalam setiap upload. File lainnya tidak akan disertakan","Upload URL might be wrong or doesn't exist.":"Alamat URL untuk upload tidak benar atau tidak ada","Uploaded %d/%d files":"File terupload %d/%d","You must specify either browse_button or drop_element.":""});