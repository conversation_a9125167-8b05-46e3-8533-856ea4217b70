<?php
session_start();

if (isset($_SESSION['username'])) {
    $username = $_SESSION['username'];
} else {
    // Redirect if no session found
    header("Location: user-login-form.html"); // Replace with actual login form file if different
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login Successful</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .success-box {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
        }
        .success-box h2 {
            color: #28a745;
            margin-bottom: 10px;
        }
        .success-box p {
            font-size: 18px;
            color: #333;
        }
    </style>
</head>
<body>

<div class="success-box">
    <h2>✅ Login Successful!</h2>
    <p>Welcome <strong><?php echo htmlspecialchars($username); ?></strong></p>
</div>

</body>
</html>
