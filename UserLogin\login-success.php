<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header("Location: user-login.html");
    exit();
}

// Get user information from session
$username = $_SESSION['username'] ?? 'User';
$first_name = $_SESSION['first_name'] ?? '';
$last_name = $_SESSION['last_name'] ?? '';
$email = $_SESSION['email'] ?? '';
$full_name = trim($first_name . ' ' . $last_name);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Login Successful</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #a1c4fd, #c2e9fb);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .success-box {
            background: #fff;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            text-align: center;
        }
        .success-box h2 {
            color: #28a745;
            margin-bottom: 10px;
        }
        .success-box p {
            font-size: 18px;
            color: #333;
        }
    </style>
</head>
<body>

<div class="success-box">
    <h2>✅ Login Successful!</h2>
    <p>Welcome <strong><?php echo htmlspecialchars($full_name ?: $username); ?></strong></p>
    <p>Email: <?php echo htmlspecialchars($email); ?></p>
    <p>Username: <?php echo htmlspecialchars($username); ?></p>

    <div style="margin-top: 20px;">
        <a href="dashboard.php" style="display: inline-block; padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;">Go to Dashboard</a>
        <a href="../index.html" style="display: inline-block; padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-right: 10px;">Go to Homepage</a>
        <a href="logout.php" style="display: inline-block; padding: 10px 20px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px;">Logout</a>
    </div>

    <script>
        // Auto-redirect to dashboard after 3 seconds
        setTimeout(function() {
            window.location.href = 'dashboard.php';
        }, 3000);
    </script>
</div>

</body>
</html>
