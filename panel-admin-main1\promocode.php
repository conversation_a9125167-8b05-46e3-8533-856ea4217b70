<?php
// $servername = "localhost";
// $username = "root";
// $password = "12345";
// $dbname = "admin_panel";

// $conn = new mysqli($servername, $username, $password, $dbname);
// if ($conn->connect_error) {
//     die("Connection failed: " . $conn->connect_error);
// }

include 'db.php';

if (isset($_GET['delete_id'])) {
    $id = $_GET['delete_id'];
    $sql = "DELETE FROM promocode WHERE promocode_id='$id'";
    // $conn->query($sql);
    $result = mysqli_query($conn  , $sql);
    if($result) {
      echo "<script>alert('Deleted'); window.location.href='promocode.php';</script>";
    }else {
      echo "<script>alert('Not Deleted');</script>";

    }
    exit;
}

$sql = "SELECT * FROM promocode";
$result = $conn->query($sql);
?>

<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promo Codes</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body> -->
<?php require_once __DIR__ . "/header.php" ?>
<div class="content container-fluid">
<div class="data-table-filters">
		<div class="row g-3">
			<div class="col-12 col-md-3">
				<div class="input-group input-group-sm">
					<div class="input-group-text">
						<i class="bi-search"></i>
					</div>
					<input type="search" id="searchInput" class="form-control customer-table-search" placeholder="Search here">
				</div>
			</div>
			<div class="col-12 col-md-6 offset-md-3">
            <div class="d-flex align-items-center gap-2">
                <button class="btn btn-sm btn-secondary ms-md-auto" data-bs-toggle="modal"
                    data-bs-target="#transactionfilterModal">
                    Filter <i class="bi bi-funnel-fill"></i>
                </button>
                <div class="export-buttons"></div>
            </div>
        </div>
    </div>

    
<!-- Filter Modal -->
<div class="modal fade" id="transactionfilterModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
		aria-labelledby="filterModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
			<div class="modal-content shadow border">
				<div class="modal-header">
					<h5 class="modal-title" id="filterModalLabel">Filter</h5>
				</div>
				<div class="modal-body py-3">
					<form class="row g-3" id="filter-form">

					<div class="col-12 col-md-6">
							<label class="form-label">Filter by Promocode</label>
							<input type="text" class="form-control form-control-sm" name="promocode" placeholder="Enter User ID">
						</div>

						<div class="col-12 col-md-6">
                        <label class="form-label">Filter by Status</label>
							<select name="status" id="edit-user-city" class="form-control form-control-sm">
								<option value="Valid">Valid</option>
								<option value="Expired">Expired</option>
							
							</select>
                        
                        </div>
                    </form>
                </div>
                <div class="modal-footer pt-0 border-top-0">
				<button type="button" onclick="clearAllFilters()" class="btn btn-sm btn-outline-danger me-auto">Clear all filters</button>                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" form="filter-form" class="btn btn-sm btn-primary">Apply Filters</button>
                </div>
            </div>
        </div>
    </div>

    </div>

<div class="container mt-4">
    <h2 class="mb-3">Promo Codes</h2>
    <table class="table table-bordered" id="data-table">
        <thead>
            <tr>
                <th>Sr.No</th>
                <th>Promo Code</th>
                <th>Start Date</th>
                <th>Expiry Date</th>
                <th>Description</th>
                <th>Status</th>
                <th>Action</th>
            </tr>
        </thead>
        <tbody>
            <?php $sr = 1; while ($row = $result->fetch_assoc()): ?>
                <tr>
                    <td><?= $sr++; ?></td>
                    <td><?= $row['promocode']; ?></td>
                    <td><?= $row['start_date']; ?></td>
                    <td><?= $row['expiry_date']; ?></td>
                    <td><?= $row['short_description']; ?></td>
                    <td><?= $row['status']; ?></td>
                    <td>
                        <select class="form-select action-select" data-id="<?= $row['promocode_id']; ?>" data-code="<?= $row['promocode']; ?>" data-start="<?= $row['start_date']; ?>" data-expiry="<?= $row['expiry_date']; ?>" data-description="<?= $row['short_description']; ?>" data-status="<?= $row['status']; ?>">
                            <option value="">Select</option>
                            <option value="edit">Edit</option>
                            <option value="delete">Delete</option>
                        </select>
                    </td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
</div>

<div class="modal fade" id="editModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Promo Code</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm"   method="POST">
                    <input type="hidden" name="promocode_id" id="edit_id">
                    <div class="mb-3">
                        <label class="form-label">Promo Code</label>
                        <input type="text" name="promocode" id="edit_code" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Start Date</label>
                        <input type="date" name="start_date" id="edit_start" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expiry Date</label>
                        <input type="date" name="expiry_date" id="edit_expiry" class="form-control">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea id="edit_description" name="short_description" class="form-control"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Status</label>
                        <select id="edit_status" name="status" class="form-control">
                            <option value="Valid">Valid</option>
                            <option value="Expired">Expired</option>
                        </select>
                    </div>
                    <button type="submit" name="promocode_update"  class="btn btn-primary">Update</button>
                </form>
             
            </div>
        </div>
    </div>
<?php require_once __DIR__ . '/footer.php' ?>

</div>

<script>



document.addEventListener("DOMContentLoaded", function () {
    const searchInput = document.getElementById("searchInput");
    const tableRows = document.querySelectorAll("#data-table tbody tr");

    searchInput.addEventListener("keyup", function () {
        const query = searchInput.value.toLowerCase();

        tableRows.forEach(row => {
            const rowText = row.textContent.toLowerCase();
            if (rowText.includes(query)) {
                row.style.display = "";
            } else {
                row.style.display = "none";
            }
        });
    });
});


$(document).ready(function() {
    $('.action-select').change(function() {
        var action = $(this).val();
        var id = $(this).data('id');
        if (action === 'edit') {
            $('#edit_id').val(id);
            $('#edit_code').val($(this).data('code'));
            $('#edit_start').val($(this).data('start'));
            $('#edit_expiry').val($(this).data('expiry'));
            $('#edit_description').val($(this).data('description'));
            $('#edit_status').val($(this).data('status'));
            $('#editModal').modal('show');
        } else if (action === 'delete') {
            if (confirm('Are you sure you want to delete this record?')) {
              window.location.href=`promocode.php?delete_id=${id}`;
                // $.post('promocode.php', { delete_id: id }, function() {
                //   alert("hello")
                  
                  
                //     location.reload();
                // });
            }
        }
    });
});


document.getElementById("editForm").addEventListener("submit", function(event) {
    event.preventDefault(); // Prevent actual form submission

    const formData = new FormData(this);
    
    // Convert to object
    const formValues = Object.fromEntries(formData.entries());

    console.log(formValues); // Logs an object with form values

  
    let promocode_id=formValues["promocode_id"];
    let promocode = formValues["promocode"];
    let start_date =formValues["start_date"];
    let expiry_date = formValues["expiry_date"];
    let short_description = formValues["short_description"];
    let status = formValues["status"];

   window.location.href=`process.php?promocode_id=${promocode_id}&promocode=${promocode}&start_date=${start_date}&expiry_date=${expiry_date}&short_description=${short_description}&status=${status}`

   
});





//Filters

function clearAllFilters() {
    // Select the form and reset all input fields
    document.getElementById("filter-form").reset();
}

//following code for applying filter 

document.getElementById("filter-form").addEventListener("submit", function (e) {
    e.preventDefault(); // Prevent form submission

    let formData = new FormData(this); // Get form data

    fetch("filter_promo.php", {
        method: "POST",
        body: formData
    })
    .then(response => response.text())
    .then(data => {
        document.querySelector("#data-table tbody").innerHTML = data; // Update table
        var modal = bootstrap.Modal.getInstance(document.getElementById('transactionfilterModal'));
        modal.hide(); // Close modal
    })
    .catch(error => console.error("Error:", error));
});






</script>

<!-- </body>
</html> -->

<?php 



$conn->close(); ?>
