// Danish (da)
plupload.addI18n({"%d files queued":"%d filer i kø","%s already present in the queue.":"%s findes allerede i køen.","%s specified, but cannot be found.":"","Add Files":"Tilføj filer","Add files to the upload queue and click the start button.":"Tilføj filer til køen og klik Start upload knappen.","b":"b","Close":"Luk","Drag files here.":"Træk filer her.","Duplicate file error.":"Filen findes allerede.","Error: File too large:":"Fejl: Filen er for stor:","Error: Invalid file extension:":"Fejl: Ugyldigt fil format:","File count error.":"Fil antal fejl.","File extension error.":"Fil format fejl.","File size error.":"Filstørrelse fejl.","File: %s":"Fil: %s","File: %s, size: %d, max file size: %d":"Fil: %s, størrelse: %d, maks. filstørrelse: %d","Filename":"Filnavn","gb":"gb","HTTP Error.":"HTTP fejl.","Image format either wrong or not supported.":"Billede format er enten forkert eller ikke understøttet.","Init error.":"Opstarts fejl.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime mangler tilgængelige hukommelse.","Select files":"Vælg filer","Size":"Størrelse","Start Upload":"Start upload","Status":"Status","Stop Upload":"Stop upload","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload accepterer kun %d fil(er) af gangen. Ekstra filer blev skippet.","Upload URL might be wrong or doesn't exist.":"Upload URL kan være forkert eller ikke eksisterende.","Uploaded %d/%d files":"Uploaded %d/%d filer","You must specify either browse_button or drop_element.":""});