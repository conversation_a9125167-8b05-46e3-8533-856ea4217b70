// Finnish (fi)
plupload.addI18n({"%d files queued":"%d tiedostoa jonossa","%s already present in the queue.":"%s on jo jonossa.","%s specified, but cannot be found.":"","Add Files":"Lisää tiedostoja","Add files to the upload queue and click the start button.":"Lisää tiedostoja lähetysjonoon ja klikkaa aloita-nappia.","b":"B","Close":"Sulje","Drag files here.":"Raahaa tiedostot tähän.","Duplicate file error.":"Tuplatiedostovirhe.","Error: File too large:":"Virhe: <PERSON><PERSON> suuri tiedosto:","Error: Invalid file extension:":"Virhe: Virheellinen tiedostopääte:","File count error.":"Tiedostolaskentavirhe.","File extension error.":"Tiedostopäätevirhe.","File size error.":"Tiedostokokovirhe.","File: %s":"Tiedosto: %s","File: %s, size: %d, max file size: %d":"Tiedosto: %s, koko: %d, suurin sallittu tiedostokoko: %d","Filename":"Tiedostonimi","gb":"GB","HTTP Error.":"HTTP-virhe.","Image format either wrong or not supported.":"Kuvaformaatti on joko väärä tai ei tuettu.","Init error.":"Init virhe.","kb":"kB","List":"","mb":"MB","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Toiminnon käytettävissä oleva muisti loppui kesken.","Select files":"Valitse tiedostoja","Size":"Koko","Start Upload":"Aloita lähetys","Status":"Tila","Stop Upload":"Pysäytä lähetys","tb":"TB","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Vain %d tiedosto(a) voidaan lähettää kerralla. Ylimääräiset tiedostot ohitettiin.","Upload URL might be wrong or doesn't exist.":"Lähetyksen URL-osoite saattaa olla väärä tai sitä ei ole olemassa.","Uploaded %d/%d files":"Lähetetty %d/%d tiedostoa","You must specify either browse_button or drop_element.":""});