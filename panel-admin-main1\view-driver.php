<?php
require_once __DIR__ . "/header.php";
require_once "db.php";

if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST['delete_id'])) {
    $id = intval($_POST['delete_id']);

    $stmt = $con->prepare("DELETE FROM drivers WHERE id = ?");
    $stmt->bind_param("i", $id);

    if ($stmt->execute()) {
        echo "success";
    } else {
        echo "error";
    }

    exit;
}

$sql = "SELECT * FROM drivers";
$result = $conn->query($sql);
?>

<div class="content container-fluid">
    <div class="page-header mb-4">
        <div class="row align-items-center">
            <div class="col">
                <h1 class="page-header-title">View Drivers</h1>
            </div>
            <div class="col-auto">
                <a class="btn btn-sm btn-primary" href="add-driver.php">
                    <i class="bi-plus-circle me-1"></i> Add New Driver
                </a>
            </div>
        </div>
    </div>

    <?php if ($result->num_rows > 0): ?>
        <div class="table-responsive">
            <table class="table table-bordered table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Corporate</th>
                        <th>Service Type</th>
                        <th>Email</th>
                        <th>Country Code</th>
                        <th>Contact</th>
                        <th>Gender</th>
                        <th>State</th>
                        <th>City</th>
                        <th>Plate Number</th>
                        <th>Manufacturer</th>
                        <th>Color</th>
                        <th>Manufacturing Year</th>
                        <th>Seat Arrangement</th>
                        <th>Address</th>
                        <th>Daily Services</th>
                        <th>Rental Services</th>
                        <th>Outstation Services</th>
                        <th>UPI ID</th>
                        <th>Bank Name</th>
                        <th>IFSC</th>
                        <th>Account Number</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while($row = $result->fetch_assoc()): ?>
                    <tr>
                        <td><?= $row['id'] ?></td>
                        <td contenteditable="false" class="editable" data-field="first_name" data-id="<?= $row['id'] ?>"><?= $row['first_name'] ?></td>
                        <td contenteditable="false" class="editable" data-field="last_name" data-id="<?= $row['id'] ?>"><?= $row['last_name'] ?></td>
                        <td contenteditable="false" class="editable" data-field="corporate" data-id="<?= $row['id'] ?>"><?= $row['corporate'] ?></td>
                        <td contenteditable="false" class="editable" data-field="service_type" data-id="<?= $row['id'] ?>"><?= $row['service_type'] ?></td>
                        <td contenteditable="false" class="editable" data-field="email" data-id="<?= $row['id'] ?>"><?= $row['email'] ?></td>
                        <td contenteditable="false" class="editable" data-field="country_code" data-id="<?= $row['id'] ?>"><?= $row['country_code'] ?></td>
                        <td contenteditable="false" class="editable" data-field="contact" data-id="<?= $row['id'] ?>"><?= $row['contact'] ?></td>
                        <td contenteditable="false" class="editable" data-field="gender" data-id="<?= $row['id'] ?>"><?= $row['gender'] ?></td>
                        <td contenteditable="false" class="editable" data-field="state" data-id="<?= $row['id'] ?>"><?= $row['state'] ?></td>
                        <td contenteditable="false" class="editable" data-field="city" data-id="<?= $row['id'] ?>"><?= $row['city'] ?></td>
                        <td contenteditable="false" class="editable" data-field="plate_number" data-id="<?= $row['id'] ?>"><?= $row['plate_number'] ?></td>
                        <td contenteditable="false" class="editable" data-field="manufacturer" data-id="<?= $row['id'] ?>"><?= $row['manufacturer'] ?></td>
                        <td contenteditable="false" class="editable" data-field="color" data-id="<?= $row['id'] ?>"><?= $row['color'] ?></td>
                        <td contenteditable="false" class="editable" data-field="manufacturing_year" data-id="<?= $row['id'] ?>"><?= $row['manufacturing_year'] ?></td>
                        <td contenteditable="false" class="editable" data-field="seat_arrangement" data-id="<?= $row['id'] ?>"><?= $row['seat_arrangement'] ?></td>
                        <td contenteditable="false" class="editable" data-field="address" data-id="<?= $row['id'] ?>"><?= $row['address'] ?></td>
                        <td contenteditable="false" class="editable" data-field="daily_services" data-id="<?= $row['id'] ?>"><?= $row['daily_services'] ?></td>
                        <td contenteditable="false" class="editable" data-field="rental_services" data-id="<?= $row['id'] ?>"><?= $row['rental_services'] ?></td>
                        <td contenteditable="false" class="editable" data-field="outstation_services" data-id="<?= $row['id'] ?>"><?= $row['outstation_services'] ?></td>
                        <td contenteditable="false" class="editable" data-field="upi_id" data-id="<?= $row['id'] ?>"><?= $row['upi_id'] ?></td>
                        <td contenteditable="false" class="editable" data-field="bank_name" data-id="<?= $row['id'] ?>"><?= $row['bank_name'] ?></td>
                        <td contenteditable="false" class="editable" data-field="ifsc" data-id="<?= $row['id'] ?>"><?= $row['ifsc'] ?></td>
                        <td contenteditable="false" class="editable" data-field="account_number" data-id="<?= $row['id'] ?>"><?= $row['account_number'] ?></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary edit-btn">Edit</button>
                            <button class="btn btn-sm btn-outline-success save-btn d-none">Save</button>
                        </td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-warning">No drivers found.</div>
    <?php endif; ?>
</div>

<?php require_once __DIR__ . "/footer.php"; ?>

<!-- Column Width Styling -->
<style>
    .table th, .table td {
        min-width: 120px;
        word-break: break-word;
        vertical-align: middle;
    }

    .table th:first-child, .table td:first-child {
        min-width: 60px;
    }

    .table th:last-child, .table td:last-child {
        min-width: 150px;
    }
</style>

<script>
document.querySelectorAll('.edit-btn').forEach(btn => {
    btn.addEventListener('click', function () {
        const row = this.closest('tr');
        row.querySelectorAll('.editable').forEach(cell => {
            cell.contentEditable = true;
            cell.classList.add('bg-warning');
        });
        this.classList.add('d-none');
        row.querySelector('.save-btn').classList.remove('d-none');
    });
});

document.querySelectorAll('.save-btn').forEach(btn => {
    btn.addEventListener('click', function () {
        const row = this.closest('tr');
        const id = row.querySelector('.editable').dataset.id;
        const updates = {};

        row.querySelectorAll('.editable').forEach(cell => {
            updates[cell.dataset.field] = cell.innerText.trim();
            cell.contentEditable = false;
            cell.classList.remove('bg-warning');
        });

        fetch('update-inline-driver.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({id, updates})
        })
        .then(res => res.text())
        .then(data => {
            alert(data);
        });

        this.classList.add('d-none');
        row.querySelector('.edit-btn').classList.remove('d-none');
    });
});
</script>
