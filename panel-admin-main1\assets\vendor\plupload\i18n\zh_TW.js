// Chinese (Taiwan) (zh_TW)
plupload.addI18n({"%d files queued":"%d 個檔案加入到序列","%s already present in the queue.":"%s 已經存在目前的檔案序列。","%s specified, but cannot be found.":"找不到已選擇的 %s。","Add Files":"增加檔案","Add files to the upload queue and click the start button.":"將檔案加入上傳序列，然後點選”開始上傳“按鈕。","b":"b","Close":"關閉","Drag files here.":"把檔案拖曳到這裡。","Duplicate file error.":"錯誤：檔案重複。","Error: File too large:":"錯誤: 檔案大小太大:","Error: Invalid file extension:":"錯誤：不接受的檔案格式:","File count error.":"檔案數量錯誤。","File extension error.":"檔案副檔名錯誤。","File size error.":"錯誤：檔案大小超過限制。","File: %s":"檔案: %s","File: %s, size: %d, max file size: %d":"檔案: %s, 大小: %d, 檔案大小上限: %d","Filename":"檔案名稱","gb":"gb","HTTP Error.":"HTTP 錯誤。","Image format either wrong or not supported.":"圖片格式錯誤或者不支援。","Init error.":"初始化錯誤。","kb":"kb","List":"清單","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"圖片解析度超出範圍！ <b>%s</b> 最高只支援到 %wx%hpx。","Runtime ran out of available memory.":"執行時耗盡了所有可用的記憶體。","Select files":"選擇檔案","Size":"大小","Start Upload":"開始上傳","Status":"狀態","Stop Upload":"停止上傳","tb":"tb","Thumbnails":"縮圖","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"每次只能上傳 %d 個檔案，超過限制數量的檔案將被忽略。","Upload URL might be wrong or doesn't exist.":"檔案URL可能有誤或者不存在。","Uploaded %d/%d files":"已上傳 %d/%d 個文件","You must specify either browse_button or drop_element.":"您必須指定 browse_button 或 drop_element。"});