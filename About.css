.u-section-1 .u-sheet-1 {
  min-height: 400px;
  margin-top: 0;
}
.about_section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.detail-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30px;
  border-radius: 10px;
  width: 100%;
  max-width: 1200px;
}

.text-content {
  width: 60%;
}

.img-box {
  width: 35%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.img-box img {
  max-width: 100%;
  height: auto;
  max-height: 90%; /* Adjust this value to control image size */
  object-fit: contain;
}
/* Vision & Mission Section */
.vision-mission {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
  padding: 20px;
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ccc0c0; 
  padding: 10px 20px;
  font-size: 14px;
}
.social-media a {
  margin-left: 15px;
  text-decoration: none;
  color: rgb(0, 0, 0);
  font-size: 18px;
  transition: color 0.3s ease-in-out;
}

.social-media a:hover {
  color: lightblue;
}



footer {
  background-color: #f8f9fc;
  padding: 40px 20px;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: auto;
}

.footer-section {
  width: 22%;
  min-width: 250px;
  margin-bottom: 20px;
}

.footer-section h4 {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
  color: black;
}

.footer-section p, 
.footer-section ul li a {
  font-size: 14px;
  color: black;
  transition: 0.3s;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a:hover {
  color: #007bff;
}

.company-info img {
  width: 50px;
  margin-bottom: 10px;
}

.company-info h3 {
  font-size: 18px;
  color: black;
}

.company-info p {
  font-size: 13px;
  color: black;
}

.social-icons {
  margin-top: 10px;
}

.social-icons a {
  font-size: 18px;
  color: #007bff;
  margin-right: 12px;
  transition: 0.3s;
  display: inline-block;
}

.social-icons a:hover {
  color: black;
  transform: scale(1.1);
}

.contact p {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.contact i {
  color: #007bff;
  font-size: 16px;
}

.footer-bottom {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #ddd;
  margin-top: 20px;
}

.footer-bottom p {
  font-size: 13px;
  color: black;
}

.policy-links a {
  text-decoration: none;
  color: #007bff;
  font-size: 13px;
}

.policy-links a:hover {
  text-decoration: underline;
  color: black;
}
.u-logo-image-1 {
  max-height: 50px; /* Adjust logo size */

}
.card {
  flex: 1;
  min-width: 300px;
  max-width: 500px;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease-in-out;
}

.card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.568);
}

.card h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 15px;
}

.card p {
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}

.card.dark h2{
  font-size: 24px;
  color: #333;
  margin-bottom: 15px;}

 .card.dark p {
  font-size: 16px;
  color: #555;
  line-height: 1.5;
}
