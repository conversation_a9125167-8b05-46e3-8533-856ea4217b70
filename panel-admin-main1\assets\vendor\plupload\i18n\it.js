// Italian (it)
plupload.addI18n({"%d files queued":"%d file in coda","%s already present in the queue.":"%s già presente nella coda.","%s specified, but cannot be found.":"%s specificato, ma non è stato possibile trovarlo.","Add Files":"Aggiungi file","Add files to the upload queue and click the start button.":"Aggiungi i file alla coda di caricamento e clicca il pulsante di avvio.","b":"byte","Close":"Chiudi","Drag files here.":"Trascina i files qui.","Duplicate file error.":"Errore file duplicato.","Error: File too large:":"Errore: File troppo grande:","Error: Invalid file extension:":"Errore: Estensione file non valida:","File count error.":"File count error.","File extension error.":"Errore estensione file.","File size error.":"Errore dimensione file.","File: %s":"File: %s","File: %s, size: %d, max file size: %d":"File: %s, dimensione: %d, dimensione max file: %d","Filename":"Nome file","gb":"gb","HTTP Error.":"Errore HTTP.","Image format either wrong or not supported.":"Formato immagine errato o non supportato.","Init error.":"Errore inizializzazione.","kb":"kb","List":"Lista","mb":"mb","N/A":"N/D","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Risoluzione oltre i limiti! <b>%s</b> runtime supporta immagini fino a %wx%hpx.","Runtime ran out of available memory.":"Runtime ha esaurito la memoria disponibile.","Select files":"Seleziona i files","Size":"Dimensione","Start Upload":"Inizia Upload","Status":"Stato","Stop Upload":"Ferma Upload","tb":"tb","Thumbnails":"Anteprime","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"URL di Upload errata o non esistente","Uploaded %d/%d files":"Caricati %d/%d file","You must specify either browse_button or drop_element.":"Devi indicare almeno uno tra browse_button o drop_element."});