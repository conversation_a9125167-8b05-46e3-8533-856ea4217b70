<?php
require_once "db.php";

$data = json_decode(file_get_contents("php://input"), true);
$id = $data['id'];
$updates = $data['updates'];

$setClause = [];
$params = [];
$types = "";

foreach ($updates as $field => $value) {
    $setClause[] = "$field = ?";
    $params[] = $value;
    $types .= "s";
}

$params[] = $id;
$types .= "i";

$sql = "UPDATE drivers SET " . implode(", ", $setClause) . " WHERE id = ?";
$stmt = $con->prepare($sql);
$stmt->bind_param($types, ...$params);

if ($stmt->execute()) {
    echo "Driver updated successfully!";
} else {
    echo "Error updating driver.";
}
?>
