<?php
$host = "localhost";
$username = "root";
$password = "";
$dbname = "admin_panel"; // ✅ YOUR correct database name

$conn = mysqli_connect($host, $username, $password, $dbname);
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Get values from form (with simple validation)
$user = $_POST['username'] ?? '';
$fname = $_POST['first_name'] ?? '';
$lname = $_POST['last_name'] ?? '';
$email = $_POST['email'] ?? '';
$phone = $_POST['phone'] ?? '';
$pass = password_hash($_POST['password'], PASSWORD_DEFAULT); // 🔐 Hash the password

// Insert into DB
$sql = "INSERT INTO users (username, first_name, last_name, email, phone, password)
        VALUES ('$user', '$fname', '$lname', '$email', '$phone', '$pass')";

if (mysqli_query($conn, $sql)) {
    // Redirect to success page
    header("Location: user-registar-success.html");
    exit;
} else {
    echo "Error: " . mysqli_error($conn);
}

mysqli_close($conn);
?>
