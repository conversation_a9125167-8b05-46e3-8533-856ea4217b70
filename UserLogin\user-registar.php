<?php
session_start();

$host = "localhost";
$username = "root";
$password = "";
$dbname = "admin_panel";

// Create connection
$conn = new mysqli($host, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Create user_accounts table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS user_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20) NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
)";

if (!$conn->query($create_table_sql)) {
    die("Error creating table: " . $conn->error);
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get and validate form data
    $user = trim($_POST['username'] ?? '');
    $fname = trim($_POST['first_name'] ?? '');
    $lname = trim($_POST['last_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $pass = $_POST['password'] ?? '';

    // Basic validation
    $errors = [];

    if (empty($user)) {
        $errors[] = "Username is required";
    } elseif (strlen($user) < 3) {
        $errors[] = "Username must be at least 3 characters long";
    }

    if (empty($fname)) {
        $errors[] = "First name is required";
    }

    if (empty($lname)) {
        $errors[] = "Last name is required";
    }

    if (empty($email)) {
        $errors[] = "Email is required";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }

    if (empty($phone)) {
        $errors[] = "Phone number is required";
    } elseif (!preg_match('/^[0-9]{10,15}$/', $phone)) {
        $errors[] = "Phone number must be 10-15 digits";
    }

    if (empty($pass)) {
        $errors[] = "Password is required";
    } elseif (strlen($pass) < 6) {
        $errors[] = "Password must be at least 6 characters long";
    }

    // Check if username or email already exists
    if (empty($errors)) {
        $check_stmt = $conn->prepare("SELECT id FROM user_accounts WHERE username = ? OR email = ?");
        $check_stmt->bind_param("ss", $user, $email);
        $check_stmt->execute();
        $result = $check_stmt->get_result();

        if ($result->num_rows > 0) {
            $errors[] = "Username or email already exists";
        }
        $check_stmt->close();
    }

    // If no errors, insert the user
    if (empty($errors)) {
        $hashed_password = password_hash($pass, PASSWORD_DEFAULT);

        $stmt = $conn->prepare("INSERT INTO user_accounts (username, first_name, last_name, email, phone, password) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $user, $fname, $lname, $email, $phone, $hashed_password);

        if ($stmt->execute()) {
            $stmt->close();
            $conn->close();
            header("Location: user-registar-success.html");
            exit;
        } else {
            $errors[] = "Error creating account: " . $stmt->error;
        }
        $stmt->close();
    }

    // If there are errors, display them
    if (!empty($errors)) {
        echo "<script>alert('" . implode("\\n", $errors) . "'); window.history.back();</script>";
        exit;
    }
}

$conn->close();
?>
