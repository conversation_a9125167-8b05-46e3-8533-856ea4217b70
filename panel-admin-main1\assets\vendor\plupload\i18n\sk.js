// Slovak (sk)
plupload.addI18n({"%d files queued":"%d súborov pridaných do zoznamu","%s already present in the queue.":"%s sa už nachádza v zozname.","%s specified, but cannot be found.":"","Add Files":"Pridať súbory","Add files to the upload queue and click the start button.":"Pridajte súbory do zoznamu a potom spustite nahrávanie.","b":"b","Close":"Zatvoriť","Drag files here.":"Sem pretiahnite súbory.","Duplicate file error.":"Duplicitný súbor.","Error: File too large:":"Chyba: Súbor je príli<PERSON> veľ<PERSON>:","Error: Invalid file extension:":"Error: Nesprávny typ súboru:","File count error.":"Nesprávny počet súborov.","File extension error.":"Chybný typ súboru.","File size error.":"<PERSON><PERSON>bor je pr<PERSON><PERSON> ve<PERSON>.","File: %s":"Súbor: %s","File: %s, size: %d, max file size: %d":"Súbor: %s, veľkosť: %d, max. veľkosť súboru: %d","Filename":"Názov súboru","gb":"gb","HTTP Error.":"HTTP Chyba.","Image format either wrong or not supported.":"Formát obrázku je nesprávny alebo nie je podporovaný.","Init error.":"Chyba inicializácie.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Vyberte súbory","Size":"Veľkosť","Start Upload":"Spustiť nahrávanie","Status":"Stav","Stop Upload":"Zastaviť nahrávanie","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"URL pre nahratie nie je správna alebo neexistuje.","Uploaded %d/%d files":"Nahraných %d/%d súborov","You must specify either browse_button or drop_element.":""});