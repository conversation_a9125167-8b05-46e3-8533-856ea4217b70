<?php
session_start();

// Check if driver is logged in
if (!isset($_SESSION['driver_logged_in']) || $_SESSION['driver_logged_in'] !== true) {
    header("Location: Driver-login.html");
    exit();
}

// Include database connection
require_once 'panel-admin-main1/db.php';

// Get driver info
$driverName = $_SESSION['driver_name'] ?? 'Driver';
$driverEmail = $_SESSION['driver_email'] ?? '';
?>

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>My Rides - Driver Dashboard</title>
    <link rel="stylesheet" href="Page-2.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />

  </head>
  <body>
    <div class="sidebar">
      <div>
        <h2>
          <a href="driver-dashboard.php" class="logo-link">Driver Panel</a>
        </h2>
        <ul>
            <li>
              <a href="driver-dashboard.php" class="section1">
                <i class="fas fa-home"></i> Dashboard
              </a>
            </li>
            <li>
              <a href="myrides.php" class="section1">
                <i class="fas fa-car-side"></i> My Rides
              </a>
            </li>
            <li>
              <a href="driver-logout.php" class="section1">
                <i class="fas fa-sign-out-alt"></i> Log Out
              </a>
            </li>
          </ul>
      </div>

      <div class="bottom-img">
        <img src="parcel-box.png" alt="Parcel Icon" />
      </div>
    </div>

    <div class="main">
      <!-- Hidden input for driver ID -->
      <input type="hidden" id="driver-id" value="<?php echo isset($_SESSION['driver_id']) ? htmlspecialchars($_SESSION['driver_id']) : ''; ?>">

      <div class="welcome-header">
        <h1>My Rides</h1>
        <div class="welcome-message">
          <p>Welcome, <span class="driver-name"><?php echo htmlspecialchars($driverName); ?></span>!</p>
          <p class="date-time"><?php echo date('l, F j, Y'); ?></p>
        </div>
      </div>

      <div class="section">
        <h2>Pending Parcel Requests</h2>
        <div class="table-container">
          <table>
            <thead>
              <tr>
                <th>Parcel</th>
                <th>Description</th>
                <th>Pickup</th>
                <th>Drop</th>
                <th>Fare</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody id="pending-table-body">
              <?php
              // Fetch pending requests from the database
              $sql = "SELECT * FROM parcel_requests WHERE status = 'pending' ORDER BY created_at DESC";
              $result = $conn->query($sql);

              if ($result && $result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                  echo "<tr data-request-id='{$row['id']}'>";
                  echo "<td>{$row['parcel_type']}</td>";
                  echo "<td>{$row['item_description']}</td>";
                  echo "<td>{$row['pickup_location']}</td>";
                  echo "<td>{$row['dropoff_location']}</td>";
                  echo "<td>₹{$row['price']}</td>";
                  echo "<td>
                          <button class='accept-btn' onclick='acceptRequest({$row['id']})'>Accept</button>
                          <button class='reject-btn' onclick='rejectRequest({$row['id']})'>Reject</button>
                        </td>";
                  echo "</tr>";
                }
              } else {
                echo "<tr><td colspan='6' style='text-align: center;'>No pending requests available</td></tr>";
              }
              ?>
            </tbody>
          </table>
        </div>
      </div>

      <div class="section">
        <h2>Ride History</h2>
        <div id="ride-history" class="card-list">
          <?php
          // Fetch accepted/completed requests for this driver
          $driverId = $_SESSION['driver_id'] ?? 0;
          $sql = "SELECT * FROM parcel_requests WHERE assigned_driver_id = ? AND status IN ('accepted', 'completed') ORDER BY created_at DESC";
          $stmt = $conn->prepare($sql);
          $stmt->bind_param("i", $driverId);
          $stmt->execute();
          $result = $stmt->get_result();

          if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
              $statusColor = $row['status'] === 'completed' ? '#10b981' : '#3b82f6';
              $date = date('M d, Y', strtotime($row['created_at']));

              echo "<div class='ride-card'>";
              echo "<h3>Order #{$row['id']}</h3>";
              echo "<p><strong>Date:</strong> {$date}</p>";
              echo "<p><strong>Item:</strong> {$row['item_description']}</p>";
              echo "<p><strong>From:</strong> {$row['pickup_location']}</p>";
              echo "<p><strong>To:</strong> {$row['dropoff_location']}</p>";
              echo "<p><strong>Amount:</strong> ₹{$row['price']}</p>";
              echo "<p><strong>Status:</strong> <span style='color: {$statusColor};'>".ucfirst($row['status'])."</span></p>";
              echo "</div>";
            }
          } else {
            echo "<div class='no-rides'>No ride history available</div>";
          }
          ?>
        </div>
      </div>
    </div>

    <script src="page-2.js"></script>
  </body>
</html>
