// Serbian (sr)
plupload.addI18n({"%d files queued":"%d files queued","%s already present in the queue.":"","%s specified, but cannot be found.":"","Add Files":"Dodaj fajlove","Add files to the upload queue and click the start button.":"Dodajte fajlove u listu i kliknite na dugme Start.","b":"","Close":"Close","Drag files here.":"Prevucite fajlove ovde.","Duplicate file error.":"","Error: File too large:":"Error: File too large:","Error: Invalid file extension:":"Error: Invalid file extension:","File count error.":"File count error.","File extension error.":"File extension error.","File size error.":"File size error.","File: %s":"File: %s","File: %s, size: %d, max file size: %d":"","Filename":"<PERSON>v fajla","gb":"","HTTP Error.":"HTTP Error.","Image format either wrong or not supported.":"Image format either wrong or not supported.","Init error.":"Init error.","kb":"","List":"","mb":"","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Izaberite fajlove","Size":"Veličina","Start Upload":"Počni upload","Status":"Status","Stop Upload":"Stop Upload","tb":"","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","Uploaded %d/%d files":"Snimljeno %d/%d fajlova","You must specify either browse_button or drop_element.":""});