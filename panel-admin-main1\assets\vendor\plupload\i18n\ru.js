// Russian (ru)
plupload.addI18n({"%d files queued":"В очереди %d файл(ов)","%s already present in the queue.":"%s уже присутствует в очереди.","%s specified, but cannot be found.":"%s существует, но не может быть найден.","Add Files":"Добавьте файлы","Add files to the upload queue and click the start button.":"Добавьте файлы в очередь и нажмите кнопку \"Загрузить файлы\".","b":"б","Close":"Закрыть","Drag files here.":"Перетащите файлы сюда.","Duplicate file error.":"Такой файл уже присутствует в очереди.","Error: File too large:":"Ошибка: Файл слишком большой:","Error: Invalid file extension:":"Ошибка: У файла неправильное расширение:","File count error.":"Слишком много файлов.","File extension error.":"Неправильное расширение файла.","File size error.":"Неправильный размер файла.","File: %s":"Файл: %s","File: %s, size: %d, max file size: %d":"Файл:  %s, размер: %d, макс. размер файла: %d","Filename":"Имя файла","gb":"гб","HTTP Error.":"Ошибка HTTP.","Image format either wrong or not supported.":"Формат картинки неправильный или он не поддерживается.","Init error.":"Ошибка инициализации.","kb":"кб","List":"Список","mb":"мб","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Слишком большое разрешение! <b>%s</b> поддерживаются изображения с размером не более %wx%hpx","Runtime ran out of available memory.":"Рабочая среда превысила лимит достуной памяти.","Select files":"Выберите файлы","Size":"Размер","Start Upload":"Начать загрузку","Status":"Статус","Stop Upload":"Остановить Загрузку","tb":"тб","Thumbnails":"Миниатюра","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Загрузочный элемент за раз принимает только %d файл(ов). Лишние файлы были отброшены.","Upload URL might be wrong or doesn't exist.":"Адрес заргузки неправильный или он не существует.","Uploaded %d/%d files":"Загружено %d/%d файлов","You must specify either browse_button or drop_element.":"Вы должны указать browse_button или drop_element."});