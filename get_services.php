<?php
// Database connection
$host = 'localhost';
$dbname = 'admin-panel';
$username = 'root';
$password = '';  // Update with your actual DB password if needed

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // SQL query to fetch all services
    $stmt = $pdo->prepare("SELECT id, service_name FROM services ORDER BY service_name ASC");
    $stmt->execute();

    // Fetch all rows as an associative array
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Return the services data as JSON
    echo json_encode(['status' => 'success', 'data' => $services]);
} catch (PDOException $e) {
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>
