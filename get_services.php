<?php
// Database connection
$host = 'localhost';
$dbname = 'admin_panel'; // Fixed database name (underscore instead of hyphen)
$username = 'root';
$password = '';  // Update with your actual DB password if needed

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Check if the services table exists
    $tables = $pdo->query("SHOW TABLES LIKE 'services'")->fetchAll();
    if (count($tables) === 0) {
        echo json_encode(['status' => 'error', 'message' => 'Services table does not exist']);
        exit;
    }

    // Check if there are any services in the table
    $count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
    if ($count == 0) {
        echo json_encode(['status' => 'error', 'message' => 'No services found in the database']);
        exit;
    }

    // SQL query to fetch all services
    $stmt = $pdo->prepare("SELECT id, service_name FROM services ORDER BY service_name ASC");
    $stmt->execute();

    // Fetch all rows as an associative array
    $services = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Log the number of services found
    error_log("Found " . count($services) . " services");

    // Return the services data as JSON
    echo json_encode(['status' => 'success', 'data' => $services]);
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>
