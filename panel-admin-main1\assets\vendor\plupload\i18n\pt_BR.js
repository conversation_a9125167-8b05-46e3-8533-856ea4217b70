// Portuguese (Brazil) (pt_BR)
plupload.addI18n({"%d files queued":"%d arquivo(s)","%s already present in the queue.":"%s já presentes na fila.","%s specified, but cannot be found.":"Método de envio <b>%s</b> especificado, mas não pôde ser encontrado.","Add Files":"Adicionar arquivo(s)","Add files to the upload queue and click the start button.":"Adicione os arquivos à fila e clique no botão \"Iniciar o envio\".","b":"Bytes","Close":"Fechar","Drag files here.":"Arraste os arquivos pra cá","Duplicate file error.":"Erro: Arquivo duplicado.","Error: File too large:":"Erro: Arquivo muito grande:","Error: Invalid file extension:":"Erro: Extensão de arquivo inválida:","File count error.":"Erro na contagem dos arquivos","File extension error.":"Tipo de arquivo não permitido.","File size error.":"Tamanho de arquivo não permitido.","File: %s":"Arquivo: %s","File: %s, size: %d, max file size: %d":"Arquivo: %s, Tamanho: %d , Tamanho Máximo do Arquivo: %d","Filename":"Nome do arquivo","gb":"GB","HTTP Error.":"Erro HTTP.","Image format either wrong or not supported.":"Imagem em formato desconhecido ou não permitido.","Init error.":"Erro ao iniciar.","kb":"KB","List":"Listagem","mb":"MB","N/A":"N/D","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Resolução fora de tamanho. O método de envio <b>%s</b> suporta imagens com no máximo %wx%hpx.","Runtime ran out of available memory.":"Método de envio ficou sem mem\\u00f3ria.","Select files":"Selecione os arquivos","Size":"Tamanho","Start Upload":"Iniciar o envio","Status":"Status","Stop Upload":"Parar o envio","tb":"TB","Thumbnails":"Miniaturas","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Só são aceitos %d arquivos por vez. O que passou disso foi descartado.","Upload URL might be wrong or doesn't exist.":"URL de envio incorreta ou inexistente","Uploaded %d/%d files":"%d\\/%d arquivo(s) enviados(s)","You must specify either browse_button or drop_element.":"Você deve especificar o botão para escolher(browse_button) os arquivos ou o elemento para arrastar(drop_element)."});