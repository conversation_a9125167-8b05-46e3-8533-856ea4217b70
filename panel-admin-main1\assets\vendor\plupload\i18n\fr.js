// French (fr)
plupload.addI18n({"%d files queued":"%d fichiers en attente","%s already present in the queue.":"%s déjà présent dans la file d'attente.","%s specified, but cannot be found.":"%s spécifié, mais ne peut pas être trouvé.","Add Files":"Ajouter des fichiers","Add files to the upload queue and click the start button.":"Ajoutez des fichiers à la file d'attente de téléchargement et appuyez sur le bouton 'Démarrer l'envoi'","b":"o","Close":"Fermer","Drag files here.":"Déposez les fichiers ici.","Duplicate file error.":"Erreur: Fichier déjà sélectionné.","Error: File too large:":"Erreur: Fichier trop volumineux:","Error: Invalid file extension:":"Erreur: Extension de fichier non valide:","File count error.":"Erreur: Nombre de fichiers.","File extension error.":"Erreur d'extension de fichier","File size error.":"Erreur de taille de fichier.","File: %s":"Fichier: %s","File: %s, size: %d, max file size: %d":"Fichier: %s, taille: %d, taille max. d'un fichier: %d","Filename":"Nom du fichier","gb":"Go","HTTP Error.":"Erreur HTTP.","Image format either wrong or not supported.":"Le format d'image est soit erroné soit pas géré.","Init error.":"Erreur d'initialisation.","kb":"Ko","List":"Liste","mb":"Mo","N/A":"Non applicable","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Résolution sur les frontières ! L'exécution de <b>%s</b> supporte seulement les images de %wx%hpx","Runtime ran out of available memory.":"Le traitement a manqué de mémoire disponible.","Select files":"Sélectionnez les fichiers","Size":"Taille","Start Upload":"Démarrer l'envoi","Status":"État","Stop Upload":"Arrêter l'envoi.","tb":"To","Thumbnails":"Miniatures","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Que %d fichier(s) peuvent être envoyé(s) à la fois. Les fichiers supplémentaires ont été ignorés.","Upload URL might be wrong or doesn't exist.":"L'URL d'envoi est soit erronée soit n'existe pas.","Uploaded %d/%d files":"%d fichiers sur %d ont été envoyés","You must specify either browse_button or drop_element.":"Vous devez spécifier browse_button ou drop_element."});