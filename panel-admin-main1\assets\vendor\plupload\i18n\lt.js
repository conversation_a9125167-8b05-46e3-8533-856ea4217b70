// Lithuanian (lt)
plupload.addI18n({"%d files queued":"%d bylų eilėje","%s already present in the queue.":"%s jau yra eilėje.","%s specified, but cannot be found.":"","Add Files":"<PERSON><PERSON><PERSON><PERSON> bylas","Add files to the upload queue and click the start button.":"Pridėkite bylas į įkėlimo eilę ir paspauskite starto mygtuką.","b":"b","Close":"Uždaryti","Drag files here.":"Padėti bylas čia.","Duplicate file error.":"Pasikartojanti byla.","Error: File too large:":"Klaida: Byla per didelė:","Error: Invalid file extension:":"Klaida: Netinkamas bylos plėtinys:","File count error.":"Netinkamas bylų kiekis.","File extension error.":"Netinkamas pletinys.","File size error.":"Netinkamas bylos dydis.","File: %s":"Byla: %s","File: %s, size: %d, max file size: %d":"Byla: %s, dydis: %d, galimas dydis: %d","Filename":"Bylos pavadinimas","gb":"gb","HTTP Error.":"HTTP klaida.","Image format either wrong or not supported.":"Paveiksliuko formatas klaidingas arba nebepalaikomas.","Init error.":"Įkrovimo klaida.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Išeikvota darbinė atmintis.","Select files":"Žymėti bylas","Size":"Dydis","Start Upload":"Pradėti įkėlimą","Status":"Statusas","Stop Upload":"Stabdyti įkėlimą","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Vienu metu galima įkelti tik %d bylas(ų). Papildomos bylos buvo pašalintos.","Upload URL might be wrong or doesn't exist.":"Klaidinga arba neegzistuojanti įkėlimo nuoroda.","Uploaded %d/%d files":"Įkelta bylų: %d/%d","You must specify either browse_button or drop_element.":""});