<?php
require_once __DIR__ . '/db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id = $_POST['id'];
    $customer_name = $_POST['customer_name'];
    $cancel_reason = $_POST['cancel_reason'];
    $customer_id = $_POST['customer_id'];
    $vehicle_id = $_POST['vehicle_id'];
    $driver_name = $_POST['driver_name'];
    $additional_info = $_POST['additional_info'];

    $update_sql = "UPDATE cancellations SET 
        customer_name = ?,
        cancellation_reason = ?,
        customer_id = ?,
        vehicle_id = ?,
        driver_name = ?,
        additional_info = ?
        WHERE id = ?";

    $stmt = $conn->prepare($update_sql);
    $stmt->bind_param("ssssssi", $customer_name, $cancel_reason, $customer_id, $vehicle_id, $driver_name, $additional_info, $id);
    if ($stmt->execute()) {
        $_SESSION['success_message'] = "Cancellation updated successfully.";
    } else {
        $_SESSION['error_message'] = "Error updating cancellation.";
    }
    $stmt->close();
    
    header("Location: view-cancellation.php");
    exit();
}
?>
