<?php
require_once __DIR__ . "/db.php";
require_once __DIR__ . "/header.php";

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    $sql = "SELECT * FROM cancellations WHERE id = $id";
    $result = $conn->query($sql);
    $row = $result->fetch_assoc();
}
?>

<div class="content container-fluid">
    <div class="page-header">
        <h1 class="page-header-title">Cancellation Details</h1>
    </div>

    <form id="cancellation-view-form">
        <div class="mb-3">
            <label class="form-label">Customer Name</label>
            <input type="text" name="customer_name" value="<?= $row['customer_name'] ?>" class="form-control" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">Cancellation Reason</label>
            <input type="text" name="cancel_reason" value="<?= $row['cancellation_reason'] ?>" class="form-control" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">Customer ID</label>
            <input type="text" name="customer_id" value="<?= $row['customer_id'] ?>" class="form-control" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">Vehicle ID</label>
            <input type="text" name="vehicle_id" value="<?= $row['vehicle_id'] ?>" class="form-control" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">Driver Name</label>
            <input type="text" name="driver_name" value="<?= $row['driver_name'] ?>" class="form-control" readonly>
        </div>
        <div class="mb-3">
            <label class="form-label">Additional Info</label>
            <textarea name="additional_info" class="form-control" rows="3" readonly><?= $row['additional_info'] ?></textarea>
        </div>

        <div class="mt-3">
            <a href="view-cancellation.php" class="btn btn-secondary">Back</a>
            <button type="reset" class="btn btn-secondary">Cancel</button>
        </div>
    </form>
</div>

<?php require_once __DIR__ . "/footer.php"; ?>
