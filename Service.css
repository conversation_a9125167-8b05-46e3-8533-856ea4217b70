/* General Styles */
:root {
  --primary-color: #152c69;
  --secondary-color: #facc15;
  --accent-color: #d97706;
  --text-color: #333;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --border-radius: 10px;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: #f9f9f9;
  overflow-x: hidden;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-color);
  padding: 10px 20px;
  color: white;
}

.contact-info {
  font-size: 14px;
}

.social-media a {
  color: white;
  margin-left: 15px;
  font-size: 16px;
  transition: var(--transition);
}

.social-media a:hover {
  color: var(--secondary-color);
}

/* Navigation Styles */
.navbar {
  background-color: white;
  box-shadow: var(--box-shadow);
  padding: 15px 0;
}

.navbar-brand img {
  max-height: 50px;
}

.navbar-nav .nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 10px 15px;
  transition: var(--transition);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--primary-color);
}

.login-btn {
  background-color: var(--secondary-color);
  color: var(--dark-color);
  border: none;
  padding: 8px 20px;
  border-radius: 50px;
  font-weight: 600;
  transition: var(--transition);
}

.login-btn:hover {
  background-color: var(--accent-color);
  color: white;
}

/* Hero Section */
.hero-section {
  padding: 80px 0;
  background-color: #f0f8ff;
}

.hero-section h1 {
  font-size: 3rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 20px;
}

.hero-section p {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 30px;
}

.hero-image {
  box-shadow: var(--box-shadow);
}

/* Services Section */
.services-section {
  padding: 80px 0;
  background-color: white;
}

.section-header {
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
}

.service-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 30px;
  height: 100%;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.service-card:hover {
  transform: translateY(-10px);
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  background-color: var(--primary-color);
  opacity: 0.05;
  transition: var(--transition);
  z-index: -1;
}

.service-card:hover::before {
  height: 100%;
}

.icon-box {
  width: 70px;
  height: 70px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  transition: var(--transition);
}

.service-card:hover .icon-box {
  background-color: var(--primary-color);
}

.icon-box i {
  font-size: 30px;
  color: var(--primary-color);
  transition: var(--transition);
}

.service-card:hover .icon-box i {
  color: white;
}

.service-card h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 15px;
}

.service-card p {
  color: #666;
  margin-bottom: 20px;
}

.service-link {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
}

.service-link i {
  margin-left: 5px;
  transition: var(--transition);
}

.service-link:hover {
  color: var(--accent-color);
}

.service-link:hover i {
  transform: translateX(5px);
}

/* Why Choose Us Section */
.why-choose-us {
  padding: 80px 0;
  background-color: #f8f9fa;
}

.feature-list {
  margin-top: 30px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
}

.feature-icon {
  min-width: 40px;
  height: 40px;
  background-color: var(--secondary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.feature-icon i {
  color: var(--primary-color);
  font-size: 18px;
}

.feature-text h4 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.feature-text p {
  color: #666;
  margin-bottom: 0;
}

/* Call to Action Section */
.cta-section {
  padding: 80px 0;
  background: linear-gradient(rgba(21, 44, 105, 0.9), rgba(21, 44, 105, 0.9)),
    url("images/cta-bg.jpg");
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.cta-section p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  display: inline-block;
  background-color: var(--secondary-color);
  color: var(--dark-color);
  font-weight: 600;
  padding: 12px 30px;
  border-radius: 50px;
  text-decoration: none;
  transition: var(--transition);
}

.cta-button:hover {
  background-color: white;
  color: var(--primary-color);
  transform: translateY(-3px);
}

/* Footer Styles */
.footer {
  background-color: var(--dark-color);
  color: #adb5bd;
  padding-top: 70px;
}

.footer h3 {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 25px;
}

.footer-about p {
  margin-bottom: 20px;
}

.footer-links ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 10px;
}

.footer-links a {
  color: #adb5bd;
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: var(--secondary-color);
  padding-left: 5px;
}

.footer-contact p {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.footer-contact i {
  margin-right: 10px;
  color: var(--secondary-color);
}

.copyright {
  background-color: #1a1e21;
  padding: 20px 0;
  margin-top: 50px;
  text-align: center;
}

/* Responsive Styles */
@media (max-width: 991px) {
  .hero-section {
    padding: 60px 0;
    text-align: center;
  }

  .hero-section .col-lg-6:first-child {
    margin-bottom: 40px;
  }

  .why-choose-us .col-lg-6:first-child {
    margin-bottom: 40px;
  }
}

@media (max-width: 767px) {
  .service-card {
    margin-bottom: 30px;
  }

  .cta-section h2 {
    font-size: 2rem;
  }

  .footer {
    padding-top: 50px;
  }

  .footer-about,
  .footer-links,
  .footer-contact {
    margin-bottom: 40px;
  }
}
