// Kazakh (kk)
plupload.addI18n({"%d files queued":"%d файл кезекке қойылды","%s already present in the queue.":"%s файлы кезекте бұрыннан бар.","%s specified, but cannot be found.":"","Add Files":"Файл қосу","Add files to the upload queue and click the start button.":"Жүктеу кезегіне файлдар қосып, Бастау кнопкасын басыңыз.","b":"б","Close":"Жабу","Drag files here.":"Файлдарды мына жерге тастаңыз.","Duplicate file error.":"Файл қайталамасының қатесі.","Error: File too large:":"Қате: Файл мөлшері тым үлкен:","Error: Invalid file extension:":"Қате: Файл кеңейтілуі қате:","File count error.":"Файл санының қатесі.","File extension error.":"Файл кеңейтілуінің қатесі.","File size error.":"Файл өлшемінің қатесі.","File: %s":"Файл: %s","File: %s, size: %d, max file size: %d":"Файл: %s, өлшемі: %d, макс. файл өлшемі: %d","Filename":"Файл аты","gb":"гб","HTTP Error.":"HTTP қатесі.","Image format either wrong or not supported.":"Сурет форматы қате немесе оның қолдауы жоқ.","Init error.":"Инициализация қатесі.","kb":"кб","List":"","mb":"мб","N/A":"Қ/Ж","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Орындау кезінде жады жетпей қалды.","Select files":"Файлдар таңдаңыз","Size":"Өлшемі","Start Upload":"Жүктеуді бастау","Status":"Күйі","Stop Upload":"Жүктеуді тоқтату","tb":"тб","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Жүктеу элементі бір кезде %d файл ғана жүктей алады. Артық файлдар жүктелмейді.","Upload URL might be wrong or doesn't exist.":"Жүктеуді қабылдаушы URL қате не мүлдем көрсетілмеген.","Uploaded %d/%d files":"Жүктелген: %d/%d файл","You must specify either browse_button or drop_element.":""});