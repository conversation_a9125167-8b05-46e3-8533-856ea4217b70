<?php
require_once 'db.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {

    $id = $_POST['emp_id'];
    $partner_name = $_POST['emp_name'];
    $mobile_no = $_POST['emp_mobile'];
    $email_id = $_POST['emp_email'];
    $gender = $_POST['emp_gender'];
    $address = $_POST['emp_address'];
    $outstation_services = $_POST['outstation_services'];
    $daily_services = $_POST['daily_services'];
    $upi_id = $_POST['upi_id'];
    $account_no = $_POST['account_no'];
    $bank_name = $_POST['bank_name'];
    $ifsc_code = $_POST['ifsc_code'];
    $password = $_POST['password'];
    $salary_type = $_POST['salper'];
    $salary = $_POST['emp_salary'];
    $partner_type = $_POST['partner_type'];

$sql = "UPDATE partners SET 
            partner_name = ?, 
            mobile_no = ?, 
            email_id = ?, 
            gender = ?, 
            address = ?, 
            outstation_services = ?, 
            daily_services = ?, 
            upi_id = ?, 
            account_no = ?, 
            bank_name = ?, 
            ifsc_code = ?, 
            password = ?, 
            salary_type = ?, 
            salary = ?, 
            partner_type = ?
        WHERE id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param(
    "sssssssssssssdsi",
    $partner_name, 
    $mobile_no, 
    $email_id, 
    $gender, 
    $address, 
    $outstation_services, 
    $daily_services, 
    $upi_id, 
    $account_no, 
    $bank_name, 
    $ifsc_code, 
    $password, 
    $salary_type, 
    $salary, 
    $partner_type, 
    $id
);

    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Partner updated successfully.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Error updating partner: ' . $stmt->error]);
    }

    $stmt->close();
    $conn->close();
}
?>