<?php
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: ../admin-login.php");
    exit();
}
?>

<?php require_once __DIR__ . "/header.php";
include "db.php";
?>
<div class="content container-fluid">
	<!-- Page Header -->
	<div class="page-header">
		<div class="row align-items-center">
			<div class="col">
				<h1 class="page-header-title">
					<?= translate('add_promocode') ?>
				</h1>
			</div>
			<!-- End Col -->
			<div class="col-auto">
				<a class="btn btn-sm btn-primary" href="promocode.php">
					<i class="bi-card-list me-1"></i>
					<?= translate('view_promocodes') ?>
				</a>
			</div>
			<!-- End Col -->
		</div>
		<!-- End Row -->
	</div>
	<!-- End <PERSON> Header -->

	<!-- <form action="addpromocode.php" class="row g-3"  method="POST">
		<div class="col-12 col-md-6">
			<div class="row g-3">
				<div class="col-12">
					<input type="hidden" id="customer-form-action" name="add_customer" value="">
					<label for="" class="form-label required"><?= translate('promo_code') ?></label>
					<input type="text" name="cus_name" class="form-control form-control-sm" required>
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('discount_amount') ?></label>
					<input type="tel" name="cus_mobile" class="form-control form-control-sm">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('max_usage_total') ?></label>
					<input type="number" name="cus_email" class="form-control form-control-sm">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('max_usage_per_customer') ?></label>
					<input type="number" name="cus_email" class="form-control form-control-sm">
				</div>
			</div>
		</div>
		<div class="col-12 col-md-6">
			<div class="row g-3">
				<div class="col-12">
					<label for="" class="form-label"><?= translate('type') ?></label>
					<select name="cus_gender" class="form-control form-control-sm">
						<option value="noshare"><?= translate('flat_off') ?></option>
						<option value="male"><?= translate('percent_off') ?></option>
					</select>
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('short_description') ?></label>
					<input type="text" name="" class="form-control form-control-sm" id="">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('long_description') ?></label>
					<textarea name="cus_address" class="form-control form-control-sm" rows="5"></textarea>
				</div>
			</div>
		</div>
		<div class="col-12 col-md-6">
			<label for="" class="form-label"><?= translate('start_date') ?></label>
			<input type="date" class="form-control form-control-sm" name="" id="">
		</div>
		<div class="col-12 col-md-6">
			<label for="" class="form-label"><?= translate('expiry_date') ?></label>
			<input type="date" class="form-control form-control-sm" name="" id="">
		</div>

		<div class="modal-footer p-0 border-top-0">
			<button type="submit" name="promosubmit"  class="btn btn-sm btn-primary"><?= translate('save') ?></button>
			<button type="reset"   class="btn btn-sm btn-secondary ms-2"><?= translate('reset') ?></button>
		</div>
	</form> -->

	<form action="addpromocode.php" class="row g-3" method="POST">
	<div class="col-12 col-md-6">
			<div class="row g-3">
				<div class="col-12">
					<input type="hidden" id="customer-form-action" name="add_customer" value="">
					<label for="" class="form-label required"><?= translate('promo_code') ?></label>
					<input type="text" name="promocode" class="form-control form-control-sm" required>
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('discount_amount') ?></label>
					<input type="tel" name="discount" class="form-control form-control-sm">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('max_usage_total') ?></label>
					<input type="number" name="max_total" class="form-control form-control-sm">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('max_usage_per_customer') ?></label>
					<input type="number" name="max_customer" class="form-control form-control-sm">
				</div>
			</div>
		</div>
		<div class="col-12 col-md-6">
			<div class="row g-3">
				<div class="col-12">
					<label for="" class="form-label"><?= translate('type') ?></label>
					<select name="type" class="form-control form-control-sm">
						<option value="Flat Off"><?= translate('flat_off') ?></option>
						<option value="Percent Off"><?= translate('percent_off') ?></option>
					</select>
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('short_description') ?></label>
					<input type="text" name="s_desc" class="form-control form-control-sm" id="">
				</div>
				<div class="col-12">
					<label for="" class="form-label"><?= translate('long_description') ?></label>
					<textarea name="l_desc" class="form-control form-control-sm" rows="5"></textarea>
				</div>
			</div>
		</div>
		<div class="col-12 col-md-6">
			<label for="" class="form-label"><?= translate('start_date') ?></label>
			<input type="date" class="form-control form-control-sm" name="s_date" id="">
		</div>
		<div class="col-12 col-md-6">
			<label for="" class="form-label"><?= translate('expiry_date') ?></label>
			<input type="date" class="form-control form-control-sm" name="e_date" id="">
		</div>

		<div class="modal-footer p-0 border-top-0">
			<button type="submit" name="promosubmit"  class="btn btn-sm btn-primary"><?= translate('save') ?></button>
			<button type="reset"   class="btn btn-sm btn-secondary ms-2"><?= translate('reset') ?></button>
		</div>
	</form>
	<?php 
if(isset($_POST["promosubmit"])) {
	// echo "<script>alert('submit clicked');</script>";


	// INSERT INTO `promocode` (`promocode_id`, `promocode`, `promocode_type`, `discount_amt`, `max_total`, `max_per_customer`, `short_description`, `long_description`, `start_date`, `expiry_date`) VALUES (NULL, 'ABCD1234', 'Flat Off', '11', '1', '1', 'short description', 'long description', '2025-02-03', '2025-02-12');

	$promocode = $_POST["promocode"];
	$discount = $_POST["discount"];
	$max_total = $_POST["max_total"];
	$max_customer = $_POST["max_customer"];
	$type = $_POST["type"];
	$s_desc = $_POST["s_desc"];
	$l_desc = $_POST["l_desc"];
	$s_date = $_POST["s_date"];
	$e_date = $_POST["e_date"];

	$sql="INSERT INTO `promocode` ( `promocode`, `promocode_type`, `discount_amt`, `max_total`, `max_per_customer`, `short_description`, `long_description`,`status`, `start_date`, `expiry_date`) VALUES ( '$promocode', '$type', '$discount', '$max_total', '$max_customer', '$s_desc', '$l_desc','Valid', '$s_date', '$e_date');";
	$result = mysqli_query($conn , $sql);
	if($result) {
			// echo "<script>alert('submitted');</script>";

	}
	else {
		// echo "<script>alert('not submitted');</script>";
		// printf("Query failed: %s\n", mysqli_error($conn));

	}




}
?>

</div>
<!-- End Content -->
<?php require_once __DIR__ . '/footer.php' ?>

