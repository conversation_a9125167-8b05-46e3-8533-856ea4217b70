<?php
// Database connection
$host = 'localhost';
$dbname = 'admin_panel'; 
$username = 'root';
$password = '';

echo "<h1>Checking Services Table</h1>";

try {
    // Connect to the database
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>Database connection successful</p>";
    
    // Check if the services table exists
    $tables = $pdo->query("SHOW TABLES LIKE 'services'")->fetchAll();
    if (count($tables) === 0) {
        echo "<p style='color:red'>Services table does not exist!</p>";
        exit;
    }
    
    echo "<p>Services table exists</p>";
    
    // Check if there are any services in the table
    $count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
    echo "<p>Number of services in the table: <strong>$count</strong></p>";
    
    if ($count == 0) {
        echo "<p style='color:orange'>No services found in the database. Let's add some sample services.</p>";
        
        // Add sample services
        $pdo->exec("INSERT INTO services (service_name, number_of_seats, base_fare, minimum_fare, booking_fee, tax_percentage, price_per_minute, price_per_mile_km, mileage, daily_service, outstation_service, rental_service, provider_commission, admin_commission, driver_cash_limit, service_picture) 
        VALUES 
        ('Bike Delivery', 1, 50.00, 100.00, 10.00, 5.00, 2.00, 5.00, 'Yes', 'Yes', 'No', 'No', 10.00, 5.00, 1000.00, 'bike.jpg'),
        ('Car Service', 4, 100.00, 150.00, 20.00, 5.00, 3.00, 10.00, 'Yes', 'Yes', 'Yes', 'Yes', 15.00, 10.00, 2000.00, 'car.jpg'),
        ('Van Delivery', 6, 150.00, 200.00, 30.00, 5.00, 4.00, 15.00, 'Yes', 'Yes', 'Yes', 'No', 20.00, 15.00, 3000.00, 'van.jpg'),
        ('Truck Transport', 2, 200.00, 300.00, 50.00, 5.00, 5.00, 20.00, 'Yes', 'No', 'Yes', 'No', 25.00, 20.00, 5000.00, 'truck.jpg')");
        
        echo "<p style='color:green'>Added 4 sample services to the database</p>";
        
        // Check count again
        $count = $pdo->query("SELECT COUNT(*) FROM services")->fetchColumn();
        echo "<p>New number of services in the table: <strong>$count</strong></p>";
    }
    
    // Display all services
    $services = $pdo->query("SELECT id, service_name, base_fare FROM services")->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Services in the database:</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Service Name</th><th>Base Fare</th></tr>";
    
    foreach ($services as $service) {
        echo "<tr>";
        echo "<td>" . $service['id'] . "</td>";
        echo "<td>" . $service['service_name'] . "</td>";
        echo "<td>₹" . $service['base_fare'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (PDOException $e) {
    echo "<p style='color:red'>Database error: " . $e->getMessage() . "</p>";
}
?>
