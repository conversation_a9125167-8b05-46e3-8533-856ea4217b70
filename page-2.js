// Dashboard toggle
const toggle = document.getElementById("availabilityToggle");
const statusText = document.getElementById("availabilityStatus");

if (toggle && statusText) {
  toggle.addEventListener("change", function () {
    statusText.textContent = this.checked
      ? "Currently Online"
      : "Currently Offline";
  });
}

// We no longer need sample data as we're loading from the database

// Function to refresh the pending requests table
function refreshPendingRequests() {
  // We could implement this to refresh the table via AJAX
  // For now, we'll just reload the page
  window.location.reload();
}

function acceptRequest(id) {
  if (!confirm("Are you sure you want to accept this delivery request?")) {
    return;
  }

  // Get the driver ID from the session (we'll pass it via a hidden input)
  const driverId = document.getElementById("driver-id").value;

  // Send request to the server
  fetch("update-request-status.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      request_id: id,
      driver_id: driverId,
      status: "accepted",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert("Request accepted successfully!");
        // Remove the row from the pending requests table
        const row = document.querySelector(`tr[data-request-id="${id}"]`);
        if (row) {
          row.remove();
        }

        // If no more rows, show "no pending requests" message
        const tbody = document.getElementById("pending-table-body");
        if (tbody.children.length === 0) {
          tbody.innerHTML =
            '<tr><td colspan="6" style="text-align: center;">No pending requests available</td></tr>';
        }
      } else {
        alert("Error: " + data.message);
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      alert("An error occurred while processing your request.");
    });
}

function rejectRequest(id) {
  if (!confirm("Are you sure you want to reject this delivery request?")) {
    return;
  }

  // Send request to the server
  fetch("update-request-status.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      request_id: id,
      status: "rejected",
    }),
  })
    .then((response) => response.json())
    .then((data) => {
      if (data.success) {
        alert("Request rejected.");
        // Remove the row from the pending requests table
        const row = document.querySelector(`tr[data-request-id="${id}"]`);
        if (row) {
          row.remove();
        }

        // If no more rows, show "no pending requests" message
        const tbody = document.getElementById("pending-table-body");
        if (tbody.children.length === 0) {
          tbody.innerHTML =
            '<tr><td colspan="6" style="text-align: center;">No pending requests available</td></tr>';
        }
      } else {
        alert("Error: " + data.message);
      }
    })
    .catch((error) => {
      console.error("Error:", error);
      alert("An error occurred while processing your request.");
    });
}

document.addEventListener("DOMContentLoaded", () => {
  // Initialize any UI elements or event listeners here
  console.log("Driver dashboard loaded");
});
