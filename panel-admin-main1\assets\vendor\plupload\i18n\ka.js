// Georgian (ka)
plupload.addI18n({"%d files queued":"რიგშია %d ფაილი","%s already present in the queue.":"%s უკვე დამატებულია.","%s specified, but cannot be found.":"%s მითითებულია, მაგრამ ვერ მოიძებნა.","Add Files":"დაამატეთ ფაილები","Add files to the upload queue and click the start button.":"დაამატეთ ფაილები და დააჭირეთ ღილაკს - ატვირთვა.","b":"ბ","Close":"დავხუროთ","Drag files here.":"ჩააგდეთ ფაილები აქ.","Duplicate file error.":"ესეთი ფაილი უკვე დამატებულია.","Error: File too large:":"შეცდომა: ფაილი ზედმეტად დიდია.","Error: Invalid file extension:":"შეცდომა: ფაილს აქვს არასწორი გაფართოება.","File count error.":"აღმოჩენილია ზედმეტი ფაილები.","File extension error.":"ფაილის ფორმატი დაშვებული არ არის.","File size error.":"ფაილის ზომა დაშვებულზე დიდია.","File: %s":"ფაილი: %s","File: %s, size: %d, max file size: %d":"ფაილი: %s, ზომა: %d, მაქსიმალური დაშვებული ზომა: %d","Filename":"ფაილის სახელი","gb":"გბ","HTTP Error.":"HTTP შეცდომა.","Image format either wrong or not supported.":"ფაილის ფორმატი არ არის მხარდაჭერილი ან არასწორია.","Init error.":"ინიციალიზაციის შეცდომა.","kb":"კბ","List":"","mb":"მბ","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"სურათის გარჩევადობა აღემატება <b>%s</b> გარემოს მიერ მხარდაჭერილ მქსიმუმებს - %wx%hpx.","Runtime ran out of available memory.":"ხელმისაწვდომი მეხსიერება გადაივსო.","Select files":"ფაილების მონიშვნა","Size":"ზომა","Start Upload":"ატვირთვა","Status":"სტატუსი","Stop Upload":"ატვირთვის შეჩერება","tb":"ტბ","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"ერთდროულად დაშვებულია მხოლოდ %d ფაილის დამატება.","Upload URL might be wrong or doesn't exist.":"ატვირთვის მისამართი არასწორია ან არ არსებობს.","Uploaded %d/%d files":"ატვირთულია %d/%d ფაილი","You must specify either browse_button or drop_element.":"თქვენ უნდა მიუთითოთ browse_button ან drop_element."});