// Armenian (hy)
plupload.addI18n({"%d files queued":"ցուցակում կա %d ֆայլ","%s already present in the queue.":"%s ֆայլը արդեն ավելացված է ցուցակում.","%s specified, but cannot be found.":"","Add Files":"Ավելացնել ֆայլեր","Add files to the upload queue and click the start button.":"Ավելացրեք ֆայլեր ցուցակում և սեղմեք \"Վերբեռնել\"։","b":"բ","Close":"Փակել","Drag files here.":"Տեղափոխեք ֆայլերը այստեղ","Duplicate file error.":"Ֆայլի կրկնման սխալ","Error: File too large:":"Սխալ։ Ֆայլի չափը մեծ է։","Error: Invalid file extension:":"Սխալ։ Ֆայլի ընդլայնումը սխալ է։","File count error.":"Ֆայլերի քանակի սխալ","File extension error.":"Ֆայլի ընդլայնման սխալ","File size error.":"Ֆայլի չափի սխալ","File: %s":"Ֆայլ: %s","File: %s, size: %d, max file size: %d":"Ֆայլ: %s, չափ: %d, ֆայլի մաքսիմում չափ: %d","Filename":"Ֆայլի անուն","gb":"գբ","HTTP Error.":"HTTP սխալ","Image format either wrong or not supported.":"Նկարի ֆորմատը սխալ է կամ չի ընդունվում։","Init error.":"Ստեղծման սխալ","kb":"կբ","List":"","mb":"մբ","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Օպերատիվ հիշողության անբավարարուտյուն.","Select files":"Ընտրեք ֆայլերը","Size":"Չափ","Start Upload":"Վերբեռնել","Status":"Կարգավիճակ","Stop Upload":"Կանգնեցնել","tb":"տբ","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Վերբեռնիչը միանգամից ըդունում է միայն %d ֆայլ(եր). Ավելորդ ֆայլերը հեռացվել են.","Upload URL might be wrong or doesn't exist.":"Ավեցաված URL-ը սխալ է կամ գոյություն չունի։","Uploaded %d/%d files":"Վերբեռնվել են %d/%d ֆայլերը","You must specify either browse_button or drop_element.":""});