// Chinese (China) (zh_CN)
plupload.addI18n({"%d files queued":"%d 个文件加入到队列","%s already present in the queue.":"%s 已经在当前队列里。","%s specified, but cannot be found.":"%s 已指定，但是没有找到。","Add Files":"增加文件","Add files to the upload queue and click the start button.":"将文件添加到上传队列，然后点击”开始上传“按钮。","b":"b","Close":"关闭","Drag files here.":"把文件拖到这里。","Duplicate file error.":"重复文件错误。","Error: File too large:":"错误: 文件太大:","Error: Invalid file extension:":"错误：无效的文件扩展名:","File count error.":"文件数量错误。","File extension error.":"文件扩展名错误。","File size error.":"文件大小错误。","File: %s":"文件: %s","File: %s, size: %d, max file size: %d":"文件: %s, 大小: %d, 最大文件大小: %d","Filename":"文件名","gb":"gb","HTTP Error.":"HTTP 错误。","Image format either wrong or not supported.":"图片格式错误或者不支持。","Init error.":"初始化错误。","kb":"kb","List":"列表","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"超限。<b>%s</b> 支持最大 %wx%hpx 的图片。","Runtime ran out of available memory.":"运行时已消耗所有可用内存。","Select files":"选择文件","Size":"大小","Start Upload":"开始上传","Status":"状态","Stop Upload":"停止上传","tb":"tb","Thumbnails":"缩略图","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"每次只接受同时上传 %d 个文件，多余的文件将会被删除。","Upload URL might be wrong or doesn't exist.":"上传的URL可能是错误的或不存在。","Uploaded %d/%d files":"已上传 %d/%d 个文件","You must specify either browse_button or drop_element.":"您必须指定 browse_button 或者 drop_element。"});