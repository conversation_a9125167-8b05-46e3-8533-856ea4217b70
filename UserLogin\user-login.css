* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html, body {
    height: 100%;
    font-family: Arial, sans-serif;
  }
  
  /* Background Image */
  body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('converted_image.png') no-repeat center center fixed;
    background-size: cover;
    filter: blur(4px);
    z-index: -1;
  }
  
  /* Navbar */
  .navbar {
    background-color: navy;
    color: white;
    width: 100%;
    padding: 10px 20px;
    font-size: 18px;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 999;
  }
  
  .contact-info {
    font-size: 16px;
  }
  
  .social-media a {
    color: white;
    font-size: 20px;
    margin-left: 15px;
    text-decoration: none;
    transition: 0.3s;
  }
  .social-media a:hover {
    color: lightgray;
  }
  
  /* Full screen center wrapper */
  .box-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding-top: 60px; /* Push below navbar */
  }
  

  
  /* Form Title */
  .form-box h1 {
    font-size: 26px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
  }
  
  /* Input Fields */
  .form-box input {
    width: 100%;
    margin: 10px 0;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 5px;
    font-size: 16px;
  }
  
  /* Submit Button */
  .form-box button {
    width: 100%;
    padding: 12px;
    background: #facc15;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    margin-top: 10px;
  }
  .form-box button:hover {
    background: darkorange;
  }
  
  /* Register/Login link */
  .register-msg {
    margin-top: 15px;
    font-size: 14px;
  }
  .register-msg a {
    color: navy;
    font-weight: bold;
    text-decoration: underline;
  }
  
  /* Center the registration form just like login */

  .form-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding-top: 60px; /* keeps space for navbar */
  }
  
  .form-box {
    background: rgba(255, 255, 255, 0.95); /* white box with slight transparency */
    padding: 30px;
    width: 400px;
    border-radius: 12px;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.3); /* soft shadow */
    text-align: center;
    z-index: 1;
  }
  
  /* Full page background */
body {
    margin: 0;
    font-family: Arial, sans-serif;
    background: url('your-background.jpg') no-repeat center center fixed;
    background-size: cover;
  }
  
  /* Navbar styles */
  .navbar {
    background-color: navy;
    color: white;
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 2;
  }
  
  /* Social icons spacing */
  .social-media a {
    color: white;
    margin-left: 15px;
    text-decoration: none;
  }
  
  /* Form container */
  .form-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding-top: 80px; /* space for navbar */
    box-sizing: border-box;
  }
  
  /* White card-like box */
  .form-box {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px 40px;
    width: 100%;
    max-width: 400px;
    border-radius: 12px;
    box-shadow: 0px 0px 20px rgba(0, 0, 0, 0.2);
    text-align: center;
  }
  
  /* Input field styles */
  form input {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border-radius: 6px;
    border: 1px solid #ccc;
    font-size: 14px;
  }
  
  /* Submit button */
  form button {
    width: 100%;
    padding: 12px;
    margin-top: 10px;
    background-color: rgb(22, 17, 7);
    color: white;
    border: none;
    border-radius: 6px;
    font-weight: bold;
    cursor: pointer;
  }
  
  /* Link under form */
  .register-msg {
    margin-top: 15px;
    font-size: 14px;
  }
  
  .register-msg a {
    color: blue;
    text-decoration: none;
  }
  