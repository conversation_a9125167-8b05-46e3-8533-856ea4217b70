<?php
session_start();

$host = "localhost";
$username = "root";
$password = "";
$database = "admin_panel";

// Connect to DB
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Handle login
if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $email = trim($_POST["email"] ?? '');
    $password = $_POST["password"] ?? '';

    // Basic validation
    if (empty($email) || empty($password)) {
        echo "<script>alert('⚠️ Please enter both email and password.'); window.history.back();</script>";
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo "<script>alert('⚠️ Please enter a valid email address.'); window.history.back();</script>";
        exit;
    }

    // Check user in user_accounts table
    $stmt = $conn->prepare("SELECT id, username, first_name, last_name, email, password, status FROM user_accounts WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();

    $result = $stmt->get_result();
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();

        // Check if account is active
        if ($user["status"] !== 'active') {
            echo "<script>alert('❌ Your account is inactive. Please contact support.'); window.history.back();</script>";
            exit;
        }

        // Verify password
        if (password_verify($password, $user["password"])) {
            // Save user information in session
            $_SESSION['user_id'] = $user["id"];
            $_SESSION['username'] = $user["username"];
            $_SESSION['first_name'] = $user["first_name"];
            $_SESSION['last_name'] = $user["last_name"];
            $_SESSION['email'] = $user["email"];
            $_SESSION['logged_in'] = true;

            // Update last login time (optional)
            $update_stmt = $conn->prepare("UPDATE user_accounts SET updated_at = CURRENT_TIMESTAMP WHERE id = ?");
            $update_stmt->bind_param("i", $user["id"]);
            $update_stmt->execute();
            $update_stmt->close();

            $stmt->close();
            $conn->close();

            header("Location: login-success.php");
            exit();
        } else {
            echo "<script>alert('❌ Incorrect password.'); window.history.back();</script>";
        }
    } else {
        echo "<script>alert('❌ No account found with this email address.'); window.history.back();</script>";
    }

    $stmt->close();
}

$conn->close();
?>
