<?php
session_start();
require 'config.php';

header('Content-Type: application/json');

if (!isset($_SESSION['driver_id'])) {
    echo json_encode(['error' => 'Not logged in']);
    exit;
}

$driver_id = $_SESSION['driver_id'];

$stmt = $conn->prepare("SELECT availability FROM drivers WHERE id = ?");
$stmt->bind_param("i", $driver_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 1) {
    $row = $result->fetch_assoc();
    echo json_encode(['availability' => (int)$row['availability']]);
} else {
    echo json_encode(['error' => 'Driver not found']);
}

$stmt->close();
$conn->close();
?>
