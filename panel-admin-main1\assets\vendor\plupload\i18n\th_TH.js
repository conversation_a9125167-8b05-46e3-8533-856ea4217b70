// Thai (Thailand) (th_TH)
plupload.addI18n({"%d files queued":"%d ไฟล์ที่อยู่ในคิว","%s already present in the queue.":"%s อยู่ในคิวแล้ว","%s specified, but cannot be found.":"","Add Files":"เพิ่มไฟล์","Add files to the upload queue and click the start button.":"เพิ่มไฟล์ไปยังคิวอัพโหลดและคลิกที่ปุ่มเริ่ม","b":"ไบต์","Close":"ปิด","Drag files here.":"ลากไฟล์มาที่นี่","Duplicate file error.":"ไฟล์ที่ซ้ำกันเกิดข้อผิดพลาด","Error: File too large:":"ข้อผิดพลาด: ไฟล์ใหญ่เกินไป:","Error: Invalid file extension:":"ข้อผิดพลาด: นามสกุลไฟล์ไม่ถูกต้อง:","File count error.":"การนับไฟล์เกิดข้อผิดพลาด","File extension error.":"นามสกุลไฟล์เกิดข้อผิดพลาด","File size error.":"ขนาดไฟล์เกิดข้อผิดพลาด","File: %s":"ไฟล์: %s","File: %s, size: %d, max file size: %d":"ไฟล์: %s, ขนาด: %d, ขนาดไฟล์สูงสุด: %d","Filename":"ชื่อไฟล์","gb":"กิกะไบต์","HTTP Error.":"HTTP เกิดข้อผิดพลาด","Image format either wrong or not supported.":"รูปแบบรูปภาพทั้งสองผิดหรือไม่รองรับ","Init error.":"Init เกิดข้อผิดพลาด","kb":"กิโลไบต์","List":"","mb":"เมกะไบต์","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"รันไทม์วิ่งออกมาจากหน่วยความจำ","Select files":"เลือกไฟล์","Size":"ขนาด","Start Upload":"เริ่มอัพโหลด","Status":"สถานะ","Stop Upload":"หยุดอัพโหลด","tb":"เทราไบต์","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"การอัพโหลดจะยอมรับเฉพาะ %d ไฟล์(s) ในช่วงเวลาเดียวกัน เมื่อไฟล์พิเศษถูกปลดออก","Upload URL might be wrong or doesn't exist.":"URL ของการอัพโหลดอาจจะผิดหรือไม่มีอยู่","Uploaded %d/%d files":"อัพโหลดแล้ว %d/%d ไฟล์","You must specify either browse_button or drop_element.":""});