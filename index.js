let currentIndex = 0;

function moveSlide(step) {
  const slides = document.querySelectorAll(".slide");
  currentIndex += step;

  if (currentIndex < 0) {
    currentIndex = slides.length - 1;
  } else if (currentIndex >= slides.length) {
    currentIndex = 0;
  }

  document.querySelector(".slider").style.transform = `translateX(${
    -currentIndex * 100
  }%)`;
}

setInterval(() => moveSlide(1), 5000); // Auto-slide every 5s

const articles = [];
const avatar = document.getElementById("img");
const authorContainer = document.getElementById("author");
const jobContainer = document.getElementById("job");
const infoContainer = document.getElementById("info");

function setArticle({ imgSrc, author, job, info }) {
  avatar.src = imgSrc;
  avatar.title = author.toUpperCase();
  authorContainer.textContent = author;
  jobContainer.textContent = job;
  infoContainer.textContent = info;
}

function Article(imgSrc, author, job, info) {
  this.imgSrc = imgSrc;
  this.author = author;
  this.job = job;
  this.info = info;
}

articles.push(
  new Article(
    "https://res.cloudinary.com/diqqf3eq2/image/upload/v1586883334/person-1_rfzshl.jpg",
    "Susan Smith",
    "Warehouse Owner",
    "Pick Up and Pay Parcel has completely transformed the way I handle my deliveries. The website is user-friendly, and the entire process from booking to payment is seamless. The real-time tracking feature is a game-changer!"
  )
);

articles.push(
  new Article(
    "https://res.cloudinary.com/diqqf3eq2/image/upload/v1586883409/person-2_np9x5l.jpg",
    "Anna Johnson",
    "Customer",
    "An excellent service with a hassle-free experience! The website is well-designed, easy to navigate, and ensures quick parcel bookings. The secure payment system gives me peace of mind. Highly recommended! "
  )
);

articles.push(
  new Article(
    "https://res.cloudinary.com/diqqf3eq2/image/upload/v1586883417/person-3_ipa0mj.jpg",
    "Peter Jones",
    "Partner",
    " Super impressed with the efficiency of Pick Up and Pay Parcel! The interface is smooth, and booking a parcel is a breeze. Plus, the customer support team is very responsive. Keep up the great work"
  )
);

articles.push(
  new Article(
    "https://res.cloudinary.com/diqqf3eq2/image/upload/v1586883423/person-4_t9nxjt.jpg",
    "Bill Anderson",
    "Customer",
    " I love how convenient and fast this service is! The website is intuitive, making it simple to schedule pickups and track deliveries. The affordable pricing and quick service make it my go-to platform for sending parcels. "
  )
);

let curArticle = 0;
const [leftArrow, rightArrow] = document.getElementsByClassName("arrow-btn");

const showCurArticle = () => setArticle(articles[curArticle]);

leftArrow.addEventListener("click", () => {
  clearInterval(autoSlide);
  curArticle = (curArticle - 1 + articles.length) % articles.length;
  showCurArticle();
  startAutoSlide();
});

rightArrow.addEventListener("click", () => {
  clearInterval(autoSlide);
  curArticle = (curArticle + 1) % articles.length;
  showCurArticle();
  startAutoSlide();
});

document
  .getElementsByClassName("surprise-btn")[0]
  .addEventListener("click", () => {
    clearInterval(autoSlide);
    let newArticle = getRandomInt(articles.length, curArticle);
    curArticle = newArticle;
    showCurArticle();
    startAutoSlide();
  });

function getRandomInt(max, notEqual) {
  let random = Math.floor(Math.random() * max);
  return random !== notEqual ? random : getRandomInt(max, notEqual);
}

// Auto slide function
function startAutoSlide() {
  autoSlide = setInterval(() => {
    curArticle = (curArticle + 1) % articles.length;
    showCurArticle();
  }, 4000);
}

window.addEventListener("load", () => {
  showCurArticle();
  startAutoSlide();
});

// Toggle the dropdown visibility
function toggleDropdown(id) {
  // Close any open dropdowns
  document.querySelectorAll(".dropdown-content1").forEach((drop) => {
    if (drop.id !== id) drop.classList.remove("show");
  });

  // Toggle selected dropdown
  const dropdown = document.getElementById(id);
  dropdown.classList.toggle("show");
}

// Close dropdown when clicking outside
window.onclick = function (event) {
  if (!event.target.matches(".dropdown-btn1")) {
    let dropdowns = document.getElementsByClassName("dropdown-content1");
    for (let i = 0; i < dropdowns.length; i++) {
      let openDropdown = dropdowns[i];
      if (openDropdown.classList.contains("show")) {
        openDropdown.classList.remove("show");
      }
    }
  }
};

// Handle item description field
document.addEventListener("DOMContentLoaded", function () {
  const itemDescriptionField = document.getElementById("item-description");
  const clearButtons = document.querySelectorAll(".clear-input");

  // Show clear button when text is entered and trigger vehicle suggestions
  if (itemDescriptionField) {
    itemDescriptionField.addEventListener("input", function () {
      const clearButton = this.nextElementSibling;
      if (this.value.length > 0) {
        clearButton.style.display = "block";
        // Call the suggestVehicle function to provide automatic suggestions
        suggestVehicle();
      } else {
        clearButton.style.display = "none";
      }
    });

    // Initial call in case the field already has a value
    if (itemDescriptionField.value.length > 0) {
      suggestVehicle();
    }
  }

  // Handle form submission
  const bookDeliveryButton = document.querySelector(".find-courier");
  if (bookDeliveryButton) {
    bookDeliveryButton.addEventListener("click", function () {
      const pickup = document.getElementById("pickup").value;
      const dropoff = document.getElementById("dropoff").value;
      const itemDescription = document.getElementById("item-description").value;

      // Get the selected parcel type and vehicle type
      const parcelTypeBtn = document.querySelector(
        "#dropdownBox1 .dropdown-btn1 span"
      );
      const vehicleTypeBtn = document.querySelector(
        "#dropdownBox2 .dropdown-btn1 span"
      );

      const parcelType = parcelTypeBtn
        ? parcelTypeBtn.textContent
        : "Not specified";
      const vehicleType = vehicleTypeBtn
        ? vehicleTypeBtn.textContent
        : "Not specified";

      if (!pickup || !dropoff) {
        alert("Please enter both pickup and drop-off locations.");
        return;
      }

      if (!itemDescription) {
        alert("Please describe the item you want to deliver.");
        return;
      }

      // Show loading state
      bookDeliveryButton.innerHTML =
        '<i class="fas fa-spinner fa-spin"></i> Processing...';
      bookDeliveryButton.disabled = true;

      // Prepare data for submission
      const requestData = {
        pickup: pickup,
        dropoff: dropoff,
        item_description: itemDescription,
        parcel_type: parcelType,
        vehicle_type: vehicleType,
        // You could add customer info here if available
        customer_name: "Guest",
        customer_phone: "",
        customer_email: "",
      };

      // Submit data to the server using fetch API
      fetch("save-parcel-request.php", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      })
        .then((response) => response.json())
        .then((data) => {
          // Reset button
          bookDeliveryButton.innerHTML = "Book Delivery";
          bookDeliveryButton.disabled = false;

          if (data.success) {
            // Show success message with price
            alert(
              `Booking confirmed!\n\nPickup: ${pickup}\nDrop-off: ${dropoff}\nItem: ${itemDescription}\nPrice: ₹${data.price}\n\nYour request has been sent to our drivers.`
            );

            // Clear form after successful submission
            document.getElementById("pickup").value = "";
            document.getElementById("dropoff").value = "";
            document.getElementById("item-description").value = "";

            // Reset dropdown buttons
            if (parcelTypeBtn) {
              parcelTypeBtn.parentElement.innerHTML =
                '<i class="fa-solid fa-box"></i> <span>Select Parcel Type</span><i class="fas fa-chevron-down custom-small-icon"></i>';
            }

            if (vehicleTypeBtn) {
              vehicleTypeBtn.parentElement.innerHTML =
                '<i class="fa-solid fa-truck"></i> <span>Select Vehicle</span><i class="fas fa-chevron-down custom-small-icon"></i>';
            }

            // Hide clear buttons
            clearButtons.forEach((btn) => (btn.style.display = "none"));
          } else {
            // Show error message
            alert(`Error: ${data.message}\nPlease try again.`);
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          bookDeliveryButton.innerHTML = "Book Delivery";
          bookDeliveryButton.disabled = false;
          alert(
            "An error occurred while processing your request. Please try again."
          );
        });
    });
  }
});

// Simple function to select parcel type
function selectParcelType(type) {
  console.log("Selecting parcel type:", type);

  // Get the parcel type button
  const button = document.querySelector("#dropdownBox1 .dropdown-btn1");

  if (button) {
    // Update button text with icon
    button.innerHTML = `<i class="fa-solid fa-box"></i> <span>${type}</span><i class="fas fa-chevron-down custom-small-icon"></i>`;

    // Auto-suggest vehicle based on parcel type
    suggestVehicleByParcelType(type);
  } else {
    console.error("Parcel type button not found");
  }

  // Close the dropdown
  document.querySelectorAll(".dropdown-content1").forEach((dropdown) => {
    dropdown.classList.remove("show");
  });
}

// Simple function to select vehicle type
function selectVehicleType(type) {
  console.log("Selecting vehicle type:", type);

  // Get the vehicle type button
  const button = document.querySelector("#dropdownBox2 .dropdown-btn1");

  if (button) {
    // Determine the appropriate icon
    let icon = "fa-truck";
    if (type === "Bike") icon = "fa-motorcycle";
    else if (type === "Car") icon = "fa-car";
    else if (type === "Van") icon = "fa-van-shuttle";

    // Update button text with icon
    button.innerHTML = `<i class="fa-solid ${icon}"></i> <span>${type}</span><i class="fas fa-chevron-down custom-small-icon"></i>`;
  } else {
    console.error("Vehicle type button not found");
  }

  // Close the dropdown
  document.querySelectorAll(".dropdown-content1").forEach((dropdown) => {
    dropdown.classList.remove("show");
  });
}

// Legacy function for backward compatibility
function selectOption(type, buttonId = null) {
  console.log("Legacy selectOption called with:", type, buttonId);

  if (buttonId === "dropdownBox1") {
    selectParcelType(type);
  } else if (buttonId === "dropdownBox2") {
    selectVehicleType(type);
  } else {
    console.error("Unknown dropdown ID:", buttonId);
  }
}

// Smart vehicle suggestion based on item description
function suggestVehicle() {
  const itemDescriptionField = document.getElementById("item-description");
  if (!itemDescriptionField) return;

  const itemDescription = itemDescriptionField.value.toLowerCase();
  console.log("Suggesting vehicle for item description:", itemDescription);

  if (itemDescription) {
    // Keywords for different vehicle types
    const bikeKeywords = [
      "document",
      "documents",
      "letter",
      "small",
      "envelope",
      "card",
      "paper",
    ];
    const carKeywords = [
      "medium",
      "electronics",
      "laptop",
      "clothing",
      "clothes",
      "book",
      "books",
      "shoe",
      "shoes",
    ];
    const vanKeywords = [
      "large",
      "furniture",
      "chair",
      "table",
      "desk",
      "cabinet",
      "box",
      "boxes",
    ];
    const truckKeywords = [
      "heavy",
      "fragile",
      "glass",
      "refrigerator",
      "fridge",
      "washing machine",
      "sofa",
      "bed",
      "mattress",
    ];

    // Check for matches
    if (bikeKeywords.some((keyword) => itemDescription.includes(keyword))) {
      selectVehicleType("Bike");
      highlightSuggestion("Bike");
      return;
    }

    if (carKeywords.some((keyword) => itemDescription.includes(keyword))) {
      selectVehicleType("Car");
      highlightSuggestion("Car");
      return;
    }

    if (vanKeywords.some((keyword) => itemDescription.includes(keyword))) {
      selectVehicleType("Van");
      highlightSuggestion("Van");
      return;
    }

    if (truckKeywords.some((keyword) => itemDescription.includes(keyword))) {
      selectVehicleType("Truck");
      highlightSuggestion("Truck");
      return;
    }
  }
}

// Suggest vehicle based on parcel type
function suggestVehicleByParcelType(parcelType) {
  console.log("Suggesting vehicle for parcel type:", parcelType);

  switch (parcelType) {
    case "Documents":
      selectVehicleType("Bike");
      highlightSuggestion("Bike");
      break;
    case "Electronics":
      selectVehicleType("Car");
      highlightSuggestion("Car");
      break;
    case "Clothing":
      selectVehicleType("Car");
      highlightSuggestion("Car");
      break;
    case "Fragile":
      selectVehicleType("Truck");
      highlightSuggestion("Truck");
      break;
    case "Others":
      // Don't auto-select for 'others'
      break;
    default:
      // For lowercase values (backward compatibility)
      suggestVehicleByParcelType(
        parcelType.charAt(0).toUpperCase() + parcelType.slice(1)
      );
      break;
  }
}

// Highlight the suggested vehicle option
function highlightSuggestion(vehicleType) {
  console.log("Highlighting suggestion:", vehicleType);

  // Add a visual indicator that this is a suggestion
  const vehicleButton = document.querySelector("#dropdownBox2 .dropdown-btn1");
  if (vehicleButton) {
    vehicleButton.classList.add("suggested");

    // Remove the highlight after 2 seconds
    setTimeout(() => {
      vehicleButton.classList.remove("suggested");
    }, 2000);
  } else {
    console.error("Vehicle button not found for highlighting");
  }
}

// Function to clear input fields
function clearInput(element) {
  const input = element.previousElementSibling;
  input.value = "";
  element.style.display = "none";

  // If clearing the item description, reset vehicle suggestion
  if (input.id === "item-description") {
    const vehicleButton = document.querySelector(
      "#dropdownBox2 .dropdown-btn1"
    );
    vehicleButton.innerHTML = `<i class="fa-solid fa-truck"></i> <span>Select Vehicle</span><i class="fas fa-chevron-down custom-small-icon"></i>`;
  }
}

// Location suggestions functions
function getSuggestions(inputElement, listId) {
  const value = inputElement.value.toLowerCase();
  const suggestionsList = document.getElementById(listId);

  // Clear previous suggestions
  suggestionsList.innerHTML = "";

  // If input is empty, hide suggestions
  if (!value) {
    suggestionsList.style.display = "none";
    return;
  }

  // Sample Indian cities for location suggestions
  const locations = [
    "Mumbai, Maharashtra",
    "Delhi, Delhi",
    "Bangalore, Karnataka",
    "Hyderabad, Telangana",
    "Chennai, Tamil Nadu",
    "Kolkata, West Bengal",
    "Pune, Maharashtra",
    "Ahmedabad, Gujarat",
    "Jaipur, Rajasthan",
    "Lucknow, Uttar Pradesh",
    "Kanpur, Uttar Pradesh",
    "Nagpur, Maharashtra",
    "Indore, Madhya Pradesh",
    "Thane, Maharashtra",
    "Bhopal, Madhya Pradesh",
    "Visakhapatnam, Andhra Pradesh",
    "Patna, Bihar",
    "Vadodara, Gujarat",
    "Ghaziabad, Uttar Pradesh",
    "Ludhiana, Punjab",
    "Agra, Uttar Pradesh",
    "Nashik, Maharashtra",
    "Ranchi, Jharkhand",
    "Faridabad, Haryana",
    "Coimbatore, Tamil Nadu",
    "Surat, Gujarat",
    "Aurangabad, Maharashtra",
    "Kochi, Kerala",
    "Chandigarh, Punjab",
  ];

  // Filter locations based on input
  const filteredLocations = locations.filter((location) =>
    location.toLowerCase().includes(value)
  );

  // Display suggestions
  if (filteredLocations.length > 0) {
    filteredLocations.forEach((location) => {
      const li = document.createElement("li");
      li.textContent = location;
      li.onclick = function () {
        inputElement.value = location;
        suggestionsList.style.display = "none";
        // Show the clear button
        inputElement.nextElementSibling.style.display = "block";
      };
      suggestionsList.appendChild(li);
    });
    suggestionsList.style.display = "block";
  } else {
    suggestionsList.style.display = "none";
  }
}

function showSuggestions(inputElement, listId) {
  if (inputElement.value) {
    getSuggestions(inputElement, listId);
  }
}
