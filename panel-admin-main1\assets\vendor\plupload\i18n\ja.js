// Japanese (ja)
plupload.addI18n({"%d files queued":"%d ファイルが追加されました","%s already present in the queue.":"%s 既にキューに存在しています","%s specified, but cannot be found.":"指定された %s は見つかりません。","Add Files":"ファイルを追加","Add files to the upload queue and click the start button.":"ファイルをアップロードキューに追加してスタートボタンをクリックしてください","b":"B","Close":"閉じる","Drag files here.":"ここにファイルをドラッグ","Duplicate file error.":"重複ファイルエラー","Error: File too large:":"エラー: ファイルが大きすぎます:","Error: Invalid file extension:":"エラー: ファイルの拡張子が無効です：","File count error.":"ファイル数エラー","File extension error.":"ファイル拡張子エラー","File size error.":"ファイルサイズエラー","File: %s":"ファイル: %s","File: %s, size: %d, max file size: %d":"ファイル: %s, サイズ: %d, 最大ファイルサイズ: %d","Filename":"ファイル名","gb":"GB","HTTP Error.":"HTTP エラー","Image format either wrong or not supported.":"画像形式が間違っているかサポートされていません","Init error.":"イニシャライズエラー","kb":"KB","List":"リスト","mb":"MB","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"解像度がしきい値を超えています! ランタイム <b>%s</b> は縦 %h px 横 %w px までをサポートします","Runtime ran out of available memory.":"ランタイムが使用するメモリが不足しました","Select files":"ファイル選択","Size":"サイズ","Start Upload":"アップロード開始","Status":"ステータス","Stop Upload":"アップロード停止","tb":"TB","Thumbnails":"サムネイル","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"アップロード可能なファイル数は %d です 余分なファイルは削除されました","Upload URL might be wrong or doesn't exist.":"アップロード先の URL が存在しません","Uploaded %d/%d files":"アップロード中 %d/%d ファイル","You must specify either browse_button or drop_element.":"ブラウザのボタンで または ファイルをドロップするか いずれかの方法で指定する必要があります。"});