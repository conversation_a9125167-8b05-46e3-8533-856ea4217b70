// Croatian (hr)
plupload.addI18n({"%d files queued":"%d datoteka na čekanju.","%s already present in the queue.":"%s  je već prisutan  u listi čekanja.","%s specified, but cannot be found.":"","Add Files":"Dodaj datoteke","Add files to the upload queue and click the start button.":"Dodajte datoteke u listu i kliknite Upload.","b":"b","Close":"Zatvori","Drag files here.":"Dovucite datoteke ovdje","Duplicate file error.":"Pogreška dvostruke datoteke.","Error: File too large:":"Pogreška: Datoteka je prevelika:","Error: Invalid file extension:":"Pogreška: Nevažeći nastavak datoteke:","File count error.":"Pogreška u broju datoteka.","File extension error.":"Pogreška u nastavku datoteke.","File size error.":"Greška veličine datoteke.","File: %s":"Datoteka: %s","File: %s, size: %d, max file size: %d":"Datoteka: %s, veličina: %d, maksimalna veličina: %d","Filename":"Ime datoteke","gb":"gb","HTTP Error.":"HTTP greška.","Image format either wrong or not supported.":"Image format either wrong or not supported.","Init error.":"Greška inicijalizacije.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime aplikaciji  je ponestalo memorije.","Select files":"Odaberite datoteke:","Size":"Veličina","Start Upload":"Pokreni upload.","Status":"Status","Stop Upload":"Zaustavi upload.","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","Uploaded %d/%d files":"Uploadano %d/%d datoteka","You must specify either browse_button or drop_element.":""});