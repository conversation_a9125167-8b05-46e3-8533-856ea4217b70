<?php
require_once __DIR__ . "/header.php";
require_once "db.php";

if ($_SERVER["REQUEST_METHOD"] == "POST") {
	$firstName = $_POST['first_name'];
	$lastName = $_POST['last_name'];
	$corporate = $_POST['corporate'];
	$serviceType = $_POST['service_type'];
	$email = $_POST['email'];
	$countryCode = $_POST['country_code'];
	$contact = $_POST['contact'];
	$gender = $_POST['gender'];
	$state = $_POST['state'];
	$city = $_POST['city'];
	$plate = $_POST['plate_number'];
	$manufacturer = $_POST['manufacturer'];
	$color = $_POST['color'];
	$year = $_POST['manufacturing_year'];
	$seats = $_POST['seat_arrangement'];
	$address = $_POST['address'];

	$upi = $_POST['upi_id'];
	$bank = $_POST['bank_name'];
	$ifsc = $_POST['ifsc'];
	$account = $_POST['account_number'];
	$password = $_POST['password'];
	$confirm = $_POST['confirm_password'];

	if ($password === $confirm) {
		$stmt = $conn->prepare("INSERT INTO drivers (first_name, last_name, corporate, service_type, email, country_code, contact, gender, state, city, plate_number, manufacturer, color, manufacturing_year, seat_arrangement, address, upi_id, bank_name, ifsc, account_number, password) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
$stmt->bind_param("sssssssssssssssssssss", $firstName, $lastName, $corporate, $serviceType, $email, $countryCode, $contact, $gender, $state, $city, $plate, $manufacturer, $color, $year, $seats, $address, $upi, $bank, $ifsc, $account, $password);


		if ($stmt->execute()) {
			echo "<script>alert('Driver added successfully!'); window.location='view-driver.php';</script>";
		} else {
			echo "<script>alert('Error adding driver.');</script>";
		}

		$stmt->close();
	} else {
		echo "<script>alert('Passwords do not match.');</script>";
	}
}
?>

<div class="content container-fluid">
	<div class="page-header mb-4">
		<div class="row align-items-center">
			<div class="col">
				<h1 class="page-header-title">Add Driver</h1>
			</div>
			<div class="col-auto">
				<a class="btn btn-sm btn-primary" href="view-driver.php">
					<i class="bi-card-list me-1"></i> View Drivers
				</a>
			</div>
		</div>
	</div>

	<form action="" method="POST" class="row g-3">
		<?php
		$fields = [
			["first_name", "First Name", "text"],
			["last_name", "Last Name", "text"],
			["email", "Email ID", "email"],
			["country_code", "Country Code", "text"],
			["contact", "Contact Number", "text"],
			["plate_number", "Plate Number", "text"],
			["manufacturer", "Manufacturer", "text"],
			["color", "Color", "text"],
			["manufacturing_year", "Manufacturing Year", "number"],
			["seat_arrangement", "Seat Arrangement", "text"],
			["upi_id", "UPI ID", "text"],
			["bank_name", "Bank Name", "text"],
			["ifsc", "IFSC Code", "text"],
			["account_number", "Account Number", "text"],
			["password", "Password", "password"],
			["confirm_password", "Confirm Password", "password"],
		];

		foreach ($fields as $f) {
			echo '<div class="col-md-4">
				<label class="form-label" for="' . $f[0] . '">' . $f[1] . '</label>
				<input type="' . $f[2] . '" class="form-control form-control-sm" name="' . $f[0] . '" required>
			</div>';
		}
		?>

		<div class="col-md-4">
			<label class="form-label">Corporate</label>
			<select class="form-control form-control-sm" name="corporate" required>
				<option value="">Select</option>
				<option value="Company A">Company A</option>
				<option value="Company B">Company B</option>
			</select>
		</div>

		<div class="col-md-4">
			<label class="form-label">Service Type</label>
			<select class="form-control form-control-sm" name="service_type" required>
				<option value="">Select</option>
				<option value="Cab">Cab</option>
				<option value="Rental">Rental</option>
			</select>
		</div>

		<div class="col-md-4">
			<label class="form-label">Gender</label>
			<select class="form-control form-control-sm" name="gender" required>
				<option value="">Select</option>
				<option>Male</option>
				<option>Female</option>
				<option>Other</option>
			</select>
		</div>

		<div class="col-md-4">
			<label class="form-label">State</label>
			<select class="form-control form-control-sm" name="state" required>
				<option value="">Select State</option>
				<option>Maharashtra</option>
				<option>Karnataka</option>
			</select>
		</div>

		<div class="col-md-4">
			<label class="form-label">City</label>
			<select class="form-control form-control-sm" name="city" required>
				<option value="">Select City</option>
				<option>Aurangabad</option>
				<option>Pune</option>
			</select>
		</div>

		<div class="col-12">
			<label class="form-label">Full Address</label>
			<textarea class="form-control form-control-sm" name="address" required></textarea>
		</div>


		<div class="col-12 d-flex justify-content-end mt-3">
			<button type="submit" class="btn btn-sm btn-primary">Submit</button>
			<button type="reset" class="btn btn-sm btn-secondary ms-2">Reset</button>
		</div>
	</form>
</div>

<?php require_once __DIR__ . '/footer.php'; ?>
