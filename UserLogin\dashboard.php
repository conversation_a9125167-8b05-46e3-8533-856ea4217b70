<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
    header("Location: user-login.html");
    exit();
}

// Get user information from session
$username = $_SESSION['username'] ?? 'User';
$first_name = $_SESSION['first_name'] ?? '';
$last_name = $_SESSION['last_name'] ?? '';
$email = $_SESSION['email'] ?? '';
$user_id = $_SESSION['user_id'] ?? '';
$full_name = trim($first_name . ' ' . $last_name);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard</title>
    <link rel="stylesheet" href="user-login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        .dashboard-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .user-info h2 {
            margin: 0;
            color: #333;
        }

        .user-info p {
            margin: 5px 0;
            color: #666;
        }

        .dashboard-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background-color: #0056b3;
        }

        .btn-danger {
            background-color: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .dashboard-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .dashboard-card h3 {
            margin-top: 0;
            color: #333;
        }

        .dashboard-card p {
            color: #666;
            margin-bottom: 15px;
        }

        .card-icon {
            font-size: 24px;
            color: #007bff;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <div class="navbar">
        <div class="contact-info">
            📞 Call Us: +1 234 567 890 | 📧 Email: paarshinfotech.com
        </div>
        <div class="social-media">
            <a href="https://maps.app.goo.gl/3rEoYxXEq8RAEnv17" target="_blank"><i class="fa-solid fa-location-dot"></i></a>
            <a href="https://www.facebook.com/Paarsh.Infotech" target="_blank"><i class="fa-brands fa-facebook-f"></i></a>
            <a href="https://www.instagram.com/paarsh_infotech/" target="_blank"><i class="fa-brands fa-instagram"></i></a>
        </div>
    </div>

    <!-- Dashboard Container -->
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="user-info">
                <h2>Welcome, <?php echo htmlspecialchars($full_name ?: $username); ?>!</h2>
                <p><i class="fa-solid fa-envelope"></i> <?php echo htmlspecialchars($email); ?></p>
                <p><i class="fa-solid fa-user"></i> Username: <?php echo htmlspecialchars($username); ?></p>
                <p><i class="fa-solid fa-id-card"></i> User ID: <?php echo htmlspecialchars($user_id); ?></p>
            </div>
            <div class="dashboard-actions">
                <a href="../index.html" class="btn btn-primary">
                    <i class="fa-solid fa-home"></i> Homepage
                </a>
                <a href="logout.php" class="btn btn-danger">
                    <i class="fa-solid fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-box"></i>
                </div>
                <h3>Book Delivery</h3>
                <p>Schedule a new parcel delivery with our reliable service.</p>
                <a href="../index.html#parcel-form" class="btn btn-primary">Book Now</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-truck"></i>
                </div>
                <h3>Track Orders</h3>
                <p>Track your current and past delivery orders in real-time.</p>
                <a href="#" class="btn btn-primary">Track Orders</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-user-edit"></i>
                </div>
                <h3>Profile Settings</h3>
                <p>Update your personal information and account settings.</p>
                <a href="#" class="btn btn-primary">Edit Profile</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-history"></i>
                </div>
                <h3>Order History</h3>
                <p>View your complete delivery history and receipts.</p>
                <a href="#" class="btn btn-primary">View History</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-headset"></i>
                </div>
                <h3>Support</h3>
                <p>Get help with your account or delivery issues.</p>
                <a href="#" class="btn btn-primary">Contact Support</a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fa-solid fa-star"></i>
                </div>
                <h3>Rate Service</h3>
                <p>Share your feedback and rate our delivery services.</p>
                <a href="#" class="btn btn-primary">Rate Now</a>
            </div>
        </div>
    </div>
</body>
</html>
