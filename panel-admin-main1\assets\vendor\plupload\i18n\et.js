// Estonian (et)
plupload.addI18n({"%d files queued":"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on %d faili","%s already present in the queue.":"","%s specified, but cannot be found.":"","Add Files":"Add Files","Add files to the upload queue and click the start button.":"Lisa failid üleslaadimise järjekorda ja klõpsa alustamise nupule.","b":"","Close":"Sulge","Drag files here.":"Lohista failid siia.","Duplicate file error.":"","Error: File too large:":"Error: File too large:","Error: Invalid file extension:":"Error: Invalid file extension:","File count error.":"Failide arvu viga.","File extension error.":"Faililaiendi viga.","File size error.":"Failisuuruse viga.","File: %s":"Fail: %s","File: %s, size: %d, max file size: %d":"","Filename":"<PERSON><PERSON><PERSON>mi","gb":"","HTTP Error.":"HTTP ühenduse viga.","Image format either wrong or not supported.":"Image format either wrong or not supported.","Init error.":"Lähtestamise viga.","kb":"","List":"","mb":"","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Vali faile","Size":"Suurus","Start Upload":"Start Upload","Status":"Olek","Stop Upload":"Stop Upload","tb":"","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Üleslaadimise element saab vastu võtta ainult %d faili ühe korraga. Ülejäänud failid jäetakse laadimata.","Upload URL might be wrong or doesn't exist.":"Üleslaadimise URL võib olla vale või seda pole.","Uploaded %d/%d files":"Üles laaditud %d/%d","You must specify either browse_button or drop_element.":""});