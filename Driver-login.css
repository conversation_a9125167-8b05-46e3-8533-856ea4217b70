/* Reset Defaults */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

body {
  background-color: #f5f5f5;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #152c69; /* Dark blue */
  padding: 10px 20px;
  font-size: 16px;
}

.contact-info {
  color: white;
}

.social-media a {
  margin-left: 15px;
  text-decoration: none;
  color: white;
  font-size: 18px;
  transition: color 0.3s ease-in-out;
}

.social-media a:hover {
  color: lightblue;
}

/* Banner Section */
.banner {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.background-container {
  position: fixed; /* Fixes the background */
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(rgba(21, 44, 105, 0.7), rgba(21, 44, 105, 0.9)),
    url("images/delivery-man.jpg") no-repeat center center/cover;
  filter: blur(2px); /* Subtle blur effect */
  z-index: -1; /* Keeps it behind content */
}

/* Form */
.form-container {
  background-color: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0px 10px 25px rgba(0, 0, 0, 0.15);
  width: 380px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  z-index: 10;
}

.form-container h2 {
  color: #152c69;
  margin-bottom: 25px;
  font-size: 28px;
  font-weight: bold;
  position: relative;
  padding-bottom: 10px;
}

.form-container h2:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: #facc15;
}

/* Hover Effect */
.form-container:hover {
  transform: translateY(-5px);
  box-shadow: 0px 15px 30px rgba(0, 0, 0, 0.2);
}

form input,
form select {
  width: 100%;
  padding: 12px 15px;
  margin: 15px 0;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: #f9f9f9;
}

form input:focus,
form select:focus {
  outline: none;
  border-color: #152c69;
  box-shadow: 0 0 0 2px rgba(21, 44, 105, 0.1);
  background-color: white;
}

button {
  background-color: #facc15;
  color: #152c69;
  padding: 12px;
  border: none;
  width: 100%;
  cursor: pointer;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  margin-top: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(250, 204, 21, 0.2);
}

button:hover {
  background-color: #d97706;
  color: white;
  box-shadow: 0 6px 8px rgba(217, 119, 6, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  nav {
    flex-direction: column;
    text-align: center;
  }

  nav ul {
    flex-direction: column;
    padding: 10px 0;
  }

  .form-container {
    width: 90%;
  }
}
