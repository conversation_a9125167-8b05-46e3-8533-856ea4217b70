<?php
session_start();
require 'config.php';

if ($_SERVER["REQUEST_METHOD"] === "POST") {
    $email = $_POST["email"] ?? '';
    $password = $_POST["password"] ?? '';

    if (empty($email) || empty($password)) {
        echo "<script>alert('Please enter both email and password.'); window.location.href='Driver-login.html';</script>";
        exit;
    }

    $stmt = $conn->prepare("SELECT * FROM drivers WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();

    $result = $stmt->get_result();
    if ($result->num_rows === 1) {
        $driver = $result->fetch_assoc();

        // For simplicity, assuming password is stored as plain text
        // In production, you should use password_hash and password_verify
        if ($password === $driver["password"]) {
            // Save driver info in session and redirect
            $_SESSION['driver_id'] = $driver["id"];
            $_SESSION['driver_name'] = $driver["first_name"] . ' ' . $driver["last_name"];
            $_SESSION['driver_email'] = $driver["email"];
            $_SESSION['driver_logged_in'] = true;

            // Redirect to driver dashboard
            header("Location: driver-dashboard.php");
            exit();
        } else {
            echo "<script>alert('Incorrect password.'); window.location.href='Driver-login.html';</script>";
        }
    } else {
        echo "<script>alert('Driver not found. Please check your email.'); window.location.href='Driver-login.html';</script>";
    }

    $stmt->close();
}

$conn->close();
?>
