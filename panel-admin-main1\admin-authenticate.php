


<?php
session_start();
include "db.php";

$username = $_POST['username'];
$password = $_POST['password'];

// Use hashed passwords in real-world projects
$sql = "SELECT * FROM admin_users WHERE username='$username' AND password='$password'";
$result = $conn->query($sql);

if ($result->num_rows == 1) {
    $_SESSION['admin_logged_in'] = true;
    header("Location: index.php");
} else {
    echo "<script>alert('Invalid credentials'); window.location.href='admin-login.php';</script>";
}
?>
