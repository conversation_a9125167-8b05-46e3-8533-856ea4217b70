// Malay (ms)
plupload.addI18n({"%d files queued":"%d fail dalam barisan","%s already present in the queue.":"%s telah ada dalam barisan.","%s specified, but cannot be found.":"","Add Files":"Tambah Fail","Add files to the upload queue and click the start button.":"Tambah fail ke dalam giliran muat naik dan klik butang Muat Naik.","b":"b","Close":"Tutup","Drag files here.":"Seret fail ke sini.","Duplicate file error.":"Ralat menggandakan fail.","Error: File too large:":"Ralat: Fail terlalu bersar:","Error: Invalid file extension:":"Ralat: Sambungan fail tidak sah:","File count error.":"Ralat bilangan fail.","File extension error.":"Ralat sambungan fail.","File size error.":"Ralat saiz fail.","File: %s":"Fail: %s","File: %s, size: %d, max file size: %d":"Fail: %s, saiz: %d, saiz maks fail: %d","Filename":"Nama fail","gb":"gb","HTTP Error.":"Ralat HTTP.","Image format either wrong or not supported.":"Format imej sama ada salah atau tidak disokong.","Init error.":"Ralat perlaksanaan.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Ruang ingatan masa larian tidak mencukupi.","Select files":"Pilih fail","Size":"saiz","Start Upload":"Muat Naik","Status":"Status","Stop Upload":"Berhenti Muat naik","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Element muat naik hanya menerima %d fail(-fail) pada satu masa. Fail tambahan telah digugurkan.","Upload URL might be wrong or doesn't exist.":"URL muat naik mungkin salah atau tidak wujud.","Uploaded %d/%d files":"%d/%d telah dimuat naik","You must specify either browse_button or drop_element.":""});