<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Parcel Form</title>
    <link rel="stylesheet" href="styles.css">
    <script src="script.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
<style>/* Global Styles */
    body {
        font-family: Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: url('background.jpg') no-repeat center center/cover;
    }
    
    /* Parcel Form Container */
    .parcel-section {
        background: rgba(255, 255, 255, 0.2);
        padding: 20px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        backdrop-filter: blur(10px);
        width: 320px;
        text-align: center;
    }
    
    /* Form Title */
    .parcel-section h2 {
        color: orange;
        font-size: 1.2rem;
        font-weight: bold;
    }
    
    /* Input Field Styles */
    .input-group {
        display: flex;
        align-items: center;
        background: rgba(255, 255, 255, 0.3);
        padding: 10px;
        border-radius: 10px;
        margin: 10px 0;
    }
    
    .input-group i {
        font-size: 1rem;
        color: #555;
        margin-right: 10px;
    }
    
    .input-group input,
    .input-group select {
        border: none;
        background: transparent;
        width: 100%;
        font-size: 1rem;
        color: #222;
        outline: none;
    }
    
    /* Custom Dropdown */
    .dropdown {
        position: relative;
        margin: 10px 0;
    }
    
    .dropdown-btn {
        background: rgba(255, 255, 255, 0.3);
        border: none;
        padding: 12px ; /* Added right padding for spacing */
        border-radius: 10px;

        width: 100%;
        color: #555;
        font-size: 1rem;
        cursor: pointer;
        text-align: left;

    }
    span{
        margin-left: 5px;
        margin-right: 20px;
    }
    .dropdown-content {
        display: none;
        position: absolute;
        background: white;
        
        padding: 10px;
        border-radius: 8px;
        box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
        z-index: 1000;
        margin-top: 5px;
        left: 0;
    }
    
   
    
    
    /* Grid Layout for Options */
    .grid-options {
        display: grid;
        grid-template-columns: repeat(3, 5rem);
        gap: 10px;
    }
    
    .option {
        text-align: center;
        padding: 10px;
        border-radius: 8px;
        cursor: pointer;
        transition: 0.3s;
    }
    
    .option i {
        font-size: 0.8rem;
        color: #333;
    }
    
    .option p {
        font-size: 0.8rem;
        margin-top: 5px;
    }
    
    .option:hover {
        background: rgba(0, 0, 0, 0.1);
    }
    
    /* Submit Button */
    .find-courier {
        background: orange;
        color: white;
        border: none;
        padding: 12px;
        border-radius: 10px;
        width: 100%;
        font-size: 1rem;
        cursor: pointer;
        margin-top: 10px;
        transition: 0.3s;
    }
    
    .find-courier:hover {
        background: darkorange;
    }

   
    </style>

</head>
<body>

    <div class="parcel-section">
        <h2>Send Your Parcel</h2>
        <div class="parcel-inputs">
            <div class="input-group">
                <i class="fas fa-map-marker-alt"></i>
                <input type="text" id="pickup" placeholder="Pickup Location">
            </div>
            <div class="input-group">
                <i class="fas fa-map-marker-alt"></i>
                <input type="text" id="dropoff" placeholder="Drop-off Location">
            </div>

            <!-- Custom Dropdown for Parcel Type -->
            <div class="dropdown">
                
                  <button class="dropdown-btn" onclick="toggleDropdown()"><i class="fa-solid fa-box"></i> <span>Select Parcel Type</span><i class="fas fa-chevron-down fa-sm"></i></button>
                <div class="dropdown-content" id="dropdownBox">
                    <div class="grid-options">
                        <div class="option" onclick="selectOption('documents')">
                            <i class="fas fa-file-alt"></i>
                            <p>Documents</p>
                        </div>
                        <div class="option" onclick="selectOption('electronics')">
                            <i class="fas fa-tv"></i>
                            <p>Electronics</p>
                        </div>
                        <div class="option" onclick="selectOption('clothing')">
                            <i class="fas fa-tshirt"></i>
                            <p>Clothing</p>
                        </div>
                        <div class="option" onclick="selectOption('fragile')">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Fragile</p>
                        </div>
                        <div class="option" onclick="selectOption('others')">
                            <i class="fas fa-box"></i>
                            <p>Others</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="dropdown">
                
                <button class="dropdown-btn" onclick="toggleDropdown()"><i class="fa-solid fa-truck"></i> <span>Select Vehicle</span><i class="fas fa-chevron-down custom-small-icon"></i></button>
              <div class="dropdown-content" id="dropdownBox">
                  <div class="grid-options">
                      <div class="option" >
                        <i class="fa-solid fa-motorcycle"></i>                       
                         <p>Bike</p>
                      </div>
                      <div class="option" >
                        <i class="fa-solid fa-car"></i>
                          <p>Car</p>
                      </div>
                      <div class="option" >
                        <i class="fa-solid fa-van-shuttle"></i>                       
                         <p>Van</p>
                      </div>
                      <div class="option" >
                        <i class="fa-solid fa-truck"></i>
                          <p>Truck</p>
                      </div>
                     
                  </div>
              </div>
          </div>
  

            <div class="input-group">
                <i class="fas fa-truck"></i>
                <select id="vehicle-type">
                    <option value="" disabled selected>Select Vehicle</option>
                    <option value="bike">Bike</option>
                    <option value="car">Car</option>
                    <option value="van">Van</option>
                    <option value="truck">Truck</option>
                </select>
            </div>
        </div>
        <button class="find-courier">Find Courier</button>
    </div>
<script>// Toggle dropdown visibility
    function toggleDropdown() {
        const dropdown = document.getElementById("dropdownBox");
        dropdown.style.display = dropdown.style.display === "block" ? "none" : "block";
    }
    
    // Select option and update button text
    function selectOption(value) {
        document.querySelector(".dropdown-btn").innerText = value.charAt(0).toUpperCase() + value.slice(1);
        document.getElementById("dropdownBox").style.display = "none";
    }
    
    // Close dropdown when clicking outside
    document.addEventListener("click", function(event) {
        const dropdown = document.getElementById("dropdownBox");
        if (!event.target.closest(".dropdown")) {
            dropdown.style.display = "none";
        }
    });
    </script>
</body>
</html>
