<?php
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: ../admin-login.php");
    exit();
}
?>

<?php
require_once __DIR__ . "/db.php";
require_once __DIR__ . "/header.php";

if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
    $sql = "SELECT * FROM cancellations WHERE id = $id";
    $result = $conn->query($sql);
    $row = $result->fetch_assoc();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $customer_name = $_POST['customer_name'];
    $cancellation_reason = $_POST['cancel_reason'];
    $customer_id = $_POST['customer_id'];
    $vehicle_id = $_POST['vehicle_id'];
    $driver_name = $_POST['driver_name'];
    $additional_info = $_POST['additional_info'];

    $sql = "UPDATE cancellations SET 
            customer_name='$customer_name', 
            cancellation_reason='$cancellation_reason',
            customer_id='$customer_id',
            vehicle_id='$vehicle_id',
            driver_name='$driver_name',
            additional_info='$additional_info' 
            WHERE id=$id";

    if ($conn->query($sql) === TRUE) {
        echo "<script>alert('Cancellation updated successfully!'); window.location.href='view-cancellation.php';</script>";
    } else {
        echo "Error: " . $conn->error;
    }
}
?>

<div class="content container-fluid">
    <div class="page-header">
        <h1 class="page-header-title">Edit Cancellation</h1>
    </div>

    <form action="edit-cancellation.php?id=<?= $id ?>" method="POST">
        <label>Customer Name</label>
        <input type="text" name="customer_name" value="<?= $row['customer_name'] ?>" required class="form-control">

        <label>Cancellation Reason</label>
        <input type="text" name="cancel_reason" value="<?= $row['cancellation_reason'] ?>" required class="form-control">

        <label>Customer ID</label>
        <input type="text" name="customer_id" value="<?= $row['customer_id'] ?>" required class="form-control">

        <label>Vehicle ID</label>
        <input type="text" name="vehicle_id" value="<?= $row['vehicle_id'] ?>" class="form-control">

        <label>Driver Name</label>
        <input type="text" name="driver_name" value="<?= $row['driver_name'] ?>" class="form-control">

        <label>Additional Info</label>
        <textarea name="additional_info" class="form-control"><?= $row['additional_info'] ?></textarea>

        <button type="submit" class="btn btn-primary mt-3">Update</button>
        <a href="view-cancellation.php" class="btn btn-secondary mt-3">Cancel</a>
    </form>
</div>

<?php require_once __DIR__ . "/footer.php"; ?>
