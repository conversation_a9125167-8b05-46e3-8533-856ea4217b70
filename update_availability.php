<?php
session_start();
require 'config.php';

if (!isset($_SESSION['driver_id'])) {
    die("No driver logged in");
}

$driver_id = $_SESSION['driver_id'];
$availability = isset($_POST['availability']) ? (int)$_POST['availability'] : 0;

$sql = "UPDATE drivers SET availability = ? WHERE id = ?";

$stmt = $conn->prepare($sql);
$stmt->bind_param("ii", $availability, $driver_id);
$stmt->execute();

if ($stmt->affected_rows > 0) {
    echo "Success";
} else {
    echo "No changes made";
}

$stmt->close();
$conn->close();
?>
