// Greek (el)
plupload.addI18n({"%d files queued":"%d αρχεία στην ουρά","%s already present in the queue.":"Το «%s» βρίσκεται ήδη στην ουρά.","%s specified, but cannot be found.":"","Add Files":"Προσθέστε Αρχεία","Add files to the upload queue and click the start button.":"Προσθέστε αρχεία στην ουρά μεταφόρτωσης και πατήστε το κουμπί εκκίνησης.","b":"b","Close":"Κλείσιμο","Drag files here.":"Σύρετε αρχεία εδώ","Duplicate file error.":"Το αρχείο έχει ξαναπροστεθεί.","Error: File too large:":"Σφάλμα: Πολύ μεγάλο αρχείο:","Error: Invalid file extension:":"Σφάλμα: Μη έγκυρος τύπος αρχείου:","File count error.":"Σφάλμα με τον αριθμό αρχείων.","File extension error.":"Σφάλμα με τον τύπο αρχείου.","File size error.":"Σφάλμα με το μέγεθος του αρχείου.","File: %s":"Αρχείο: %s","File: %s, size: %d, max file size: %d":"Αρχείο: %s, μέγεθος: %d, μέγιστο μέγεθος αρχείου: %d","Filename":"Όνομα Αρχείου","gb":"gb","HTTP Error.":"Σφάλμα HTTP.","Image format either wrong or not supported.":"Ο τύπος εικόνας είναι λάθος ή δεν υποστηρίζεται.","Init error.":"Σφάλμα αρχικοποίησης.","kb":"kb","List":"Λίστα","mb":"mb","N/A":"Δεν ισχύει","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Δεν υπάρχει αρκετή διαθέσιμη μνήμη.","Select files":"Επιλέξτε Αρχεία","Size":"Μέγεθος","Start Upload":"Εκκίνηση Μεταφόρτωσης","Status":"Κατάσταση","Stop Upload":"Ακύρωση Μεταφόρτωσης","tb":"tb","Thumbnails":"Μικρογραφίες","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Μπορείτε να μεταφορτώσετε μεχρι και %d αρχείο/α κάθε φορά. Τα επιπλέον αρχεία αφαιρέθηκαν.","Upload URL might be wrong or doesn't exist.":"Το URL μεταφόρτωσης είναι λάθος ή δεν υπάρχει.","Uploaded %d/%d files":"Μεταφορτώθηκαν %d/%d αρχεία","You must specify either browse_button or drop_element.":""});