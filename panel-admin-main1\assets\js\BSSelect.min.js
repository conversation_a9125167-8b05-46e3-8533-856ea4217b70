/**
 * @package	BSSelect v1.5.0
 * <AUTHOR>
 * @link	https://harshalkhairnar.com
 * Copyright 2022 HitraA Technologies
 * Licensed under MIT
 **/
 !function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).BSSelect=t()}(this,function(){"use strict";function e(e,t={}){if(void 0===e)return;let s;function l(e){return!!e&&"function"!=typeof e&&"object"==typeof e&&!!e.nodeType}function o(){s.style.display="block"}if(l(e))s=e;else if("string"==typeof e&&!l(s=document.querySelector(e)))return;let n=s.getAttribute("class");n&&(n.match(/sm/)?t.size="sm":n.match(/lg/)&&(t.size="lg"));let i="",r="",a="";t.size?"sm"==t.size?(i="form-control-sm",r="form-select-sm",a="rounded-1 p-1 small"):"lg"==t.size?(i="form-control-lg",r="form-select-lg",a="rounded-2"):(i="",r="",a="rounded-3 py-1"):(i="",r="",a="rounded-2"),this.option=s.options[s.selectedIndex],this.value=s.options[s.selectedIndex].value;let c=this,d=s.options;s.id||(s.id=`bs-select-${Math.random().toString(36).slice(2)}`);let u=document.createElement("div");u.setAttribute("class","bs-select-container position-relative"),u.setAttribute("data-bs-target",s.id);let p=document.createElement("div");p.setAttribute("class",`form-select ${r}`),p.setAttribute("data-bs-toggle","collapse"),p.setAttribute("data-bs-target",`#${s.id}-rendered`),p.type="button";let f=document.createElement("div");f.setAttribute("class","selected text-nowrap"),f.value=this.value,f.innerText=this.option.innerText,f.style.textOverflow="ellipsis",f.style.overflowX="clip",p.append(f);let b=document.createElement("div");b.setAttribute("class","collapse position-absolute mt-1 w-100"),b.id=`${s.id}-rendered`,b.style.zIndex=1;let y=document.createElement("div");y.setAttribute("class","px-2 small text-nowrap card card-body");let v=document.createElement("input");v.setAttribute("class",`form-control ${i} bs-select-search`),v.type="search",v.placeholder="Search option",v.addEventListener("input",function(){let e=this.value;e.length?u.querySelectorAll(".bs-select-option").forEach(t=>{t.innerText.toLowerCase().includes(e.toLowerCase())?t.style.display="":t.style.display="none"}):u.querySelectorAll(".bs-select-option").forEach(e=>{e.style.display=""})}),y.append(v);let m=document.createElement("ul");if(m.setAttribute("class","list-group list-group-flush mt-1 border-0 bs-selector"),m.style.maxHeight="300px",m.style.overflowY="auto",m.style.overflowX="hidden",Object.values(d).forEach(e=>{let t=document.createElement("li");t.setAttribute("class",`list-group-item ${a} border-0 bs-select-option`),t.setAttribute("data-bs-value",e.value),t.style.textOverflow="ellipsis",t.style.overflowX="clip",t.style.cursor="pointer",t.innerText=e.innerText,t.addEventListener("click",function(){m.querySelectorAll(".bs-select-option").forEach(e=>{e.classList.remove("active")}),this.classList.add("active"),c.value=s.value=e.value,c.option=e,f.value=e.value,f.innerText=e.innerText,s.dispatchEvent(new Event("change"))}),t.addEventListener("mouseover",function(){this.style.backgroundColor="rgb(69 144 255 / 70%)",this.style.color="rgb(255 255 255)"}),t.addEventListener("mouseout",function(){this.style.backgroundColor="",this.style.color=""}),m.append(t)}),void 0===globalThis.bootstrap)throw ReferenceError("bootstrap not defined");y.append(m),b.append(y),u.append(p),u.append(b),s.after(u),s.style.display="none",document.addEventListener("click",function(e){!e.target.classList.contains("bs-select-search")&&bootstrap.Collapse.getInstance(b)&&bootstrap.Collapse.getInstance(b).hide()})}return e.version="1.5.0",e});