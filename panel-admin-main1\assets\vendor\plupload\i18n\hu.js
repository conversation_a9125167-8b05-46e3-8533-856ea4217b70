// Hungarian (hu)
plupload.addI18n({"%d files queued":"%d fájl sorbaállítva","%s already present in the queue.":"%s már szerepel a listában.","%s specified, but cannot be found.":"","Add Files":"Fájlok hozzáadása","Add files to the upload queue and click the start button.":"A fájlok feltöltési sorhoz való hozzáadása után az Indítás gombra kell kattintani.","b":"b","Close":"Bezárás","Drag files here.":"Ide lehet húzni a fájlokat.","Duplicate file error.":"Duplikáltfájl-hiba.","Error: File too large:":"Hiba: a fájl túl nagy:","Error: Invalid file extension:":"Hiba: érvénytelen fájlkiterjesztés:","File count error.":"A fájlok szám<PERSON><PERSON> kap<PERSON> hiba.","File extension error.":"Hibás fájlkiterjesztés.","File size error.":"Hibás fájlméret.","File: %s":"Fájl: %s","File: %s, size: %d, max file size: %d":"Fájl: %s, méret: %d, legnagyobb fájlméret: %d","Filename":"Fájlnév","gb":"GB","HTTP Error.":"HTTP-hiba.","Image format either wrong or not supported.":"Rossz vagy nem támogatott képformátum.","Init error.":"Init hiba.","kb":"kB","List":"","mb":"MB","N/A":"Nem elérhető","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Futásidőben elfogyott a rendelkezésre álló memória.","Select files":"Fájlok kiválasztása","Size":"Méret","Start Upload":"Feltöltés indítása","Status":"Állapot","Stop Upload":"Feltöltés leállítása","tb":"TB","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"A feltöltés egyszerre csak %d fájlt fogad el, a többi fájl nem lesz feltöltve.","Upload URL might be wrong or doesn't exist.":"A feltöltő URL hibás vagy nem létezik.","Uploaded %d/%d files":"Feltöltött fájlok: %d/%d","You must specify either browse_button or drop_element.":""});