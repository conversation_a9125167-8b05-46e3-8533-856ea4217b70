.reviews-section {
  --main-bg: hsl(205, 100%, 96%);
  --lightblue: hsl(205, 90%, 88%);
  --darkblue: hsl(205, 78%, 60%);
  --grey: hsl(210, 22%, 49%);
  --letter-spacing: 2px;

  min-width: 100vw;
  min-height: 100vh;
  background: var(--main-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.reviews-section .title {
  text-align: center;
}

.reviews-section .underline {
  width: 5rem;
  height: 3px;
  background: var(--darkblue);
  margin: 0.5rem auto;
}

.reviews-section .review {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.reviews-section .img-container {
  position: relative;
  display: inline-block;
  margin-bottom: 1rem;
}

.reviews-section .img-container img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--darkblue);
}

.reviews-section .img-container i {
  position: absolute;
  bottom: 0;
  right: 0;
  background: var(--darkblue);
  color: white;
  padding: 5px;
  border-radius: 50%;
}

.reviews-section p {
  color: var(--grey);
  letter-spacing: var(--letter-spacing);
}

/* Improved Arrow Buttons */
.reviews-section .arrow-buttons {
  display: flex;
  justify-content: center;
  gap: 0.7rem; /* Reduced gap for sleek look */
  margin-top: 1rem;
}

.reviews-section .arrow-btn {
  background: transparent;
  border: 2px solid var(--darkblue);
  color: var(--darkblue);
  padding: 6px 10px; /* Smaller padding */
  font-size: 14px; /* Smaller arrow size */
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.reviews-section .arrow-btn:hover {
  background: var(--darkblue);
  color: white;
}

/* Space between arrow buttons and "KNOW MORE" button */
.reviews-section .surprise-btn {
  margin-top: 1.5rem; /* Increased spacing */
  background: var(--darkblue);
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  cursor: pointer;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.reviews-section .surprise-btn:hover {
  background: var(--lightblue);
}

/* parcel section */
/* Updated Parcel Section - Fits Better in Banner */
.parcel-section {
  position: absolute;
  right: 150px;
  top: 45%; /* Adjusted for better fit */
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2); /* Soft transparency */
  color: #222; /* Darker text */
  padding: 25px; /* Reduced padding */
  width: 380px; /* Reduced width */
  border-radius: 15px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease-in-out;
}

/* Title Styling */
.parcel-section h2 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  font-weight: bold;
  color: #ff9800;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Increased gap between inputs */
.parcel-inputs {
  display: flex;
  flex-direction: column;
  gap: 15px; /* Adjusted spacing */
}

/* Input Group Styling - Reduced Icon Gap */
.input-group {
  position: relative;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  padding: 12px;
  border-radius: 10px;

  display: flex;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

/* Item Description Field Styling */
#item-description {
  flex: 1;
  width: 100%;
}

/* Highlight effect for item description */
.input-group:has(#item-description):focus-within {
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.5);
}
/* Custom Dropdown */
.dropdown1 {
  position: relative;
  width: 100%;
}

.dropdown-btn1 {
  display: flex;
  align-items: center;
  width: 100%;
  background: rgba(255, 255, 255, 0.2); /* Matches form style */
  border: none;
  padding: 12px;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  backdrop-filter: blur(5px); /* Optional: match glass effect */
  transition: all 0.3s ease;
}

/* Styling for suggested vehicle */
.dropdown-btn1.suggested {
  background: rgba(255, 152, 0, 0.3);
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.5);
  animation: pulse 1.5s ease-in-out;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.03);
  }
  100% {
    transform: scale(1);
  }
}
.custom-small-icon {
  font-size: 0.6rem;
}
span {
  margin-left: 5px;
  margin-right: 3px;
}
.dropdown-content1 {
  display: none;
  position: absolute;
  background: white;

  padding: 10px;
  border-radius: 8px;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  margin-top: 5px;
  left: 0;
}

/* Grid Layout for Options */
.grid-options {
  display: grid;
  grid-template-columns: repeat(3, 5rem);
  gap: 5px;
}

.option {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: 0.3s;
  height: 80px;
}

.option i {
  font-size: 0.8rem;
  color: #333;
}

.option p {
  font-size: 0.8rem;
  margin-top: 2px;
}

.option:hover {
  background: rgba(0, 0, 0, 0.1);
}
/* Grid Layout for Options */
.grid-options1 {
  display: grid;
  grid-template-columns: repeat(2, 5rem);
  gap: 5px;
}

.option1 {
  text-align: center;
  padding: 10px;
  border-radius: 8px;
  cursor: pointer;
  transition: 0.3s;
  height: 60px;
}

.option1 i {
  font-size: 0.8rem;
  color: #333;
}

.option1 p {
  font-size: 0.8rem;
  margin-top: 2px;
}

.option1:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Show Dropdown */
.show {
  display: block;
}

/* Adjusted icon spacing */
.input-group i {
  font-size: 1.1rem;
  color: #666;
  margin-right: 6px; /* Reduced gap from 10px to 6px */
}

/* Dark Font Color */
.input-group input,
.input-group select {
  border: none;
  background: transparent;
  color: #222;
  font-size: 1rem;
  font-weight: bold;
}

/* Adjusted Button Spacing */
.find-courier {
  width: 100%;
  background: linear-gradient(45deg, #ff9800, #ff5722);
  color: white;
  padding: 14px; /* Slightly taller button */
  font-size: 1.1rem; /* Larger font */
  font-weight: bold;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 5px 15px rgba(255, 87, 34, 0.3);
  margin-top: 20px; /* Added more spacing */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Add icon to button */
.find-courier::before {
  content: "\f0d1"; /* Truck icon */
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-right: 8px;
}

/* Button Hover Effect */
.find-courier:hover {
  background: linear-gradient(45deg, #ff5722, #d84315);
  box-shadow: 0 6px 20px rgba(255, 87, 34, 0.5);
  transform: translateY(-2px);
}
/* Suggestions Dropdown - Compact Size */
.suggestions {
  position: absolute;
  top: 100%; /* ✅ Ensures it appears below the input */
  left: 0;
  width: 100%;
  list-style-type: none;
  background: white; /* ✅ Black background */
  color: black; /* ✅ White text */
  border: 1px solid #444;
  border-radius: 5px;
  max-height: 120px; /* ✅ Reduce height */
  overflow-y: auto;
  display: none;
  z-index: 1000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-top: 4px;
  padding: 3px 0;
  font-size: 0.75rem; /* ✅ Smaller font size */
}

/* Suggestion Items - Compact Look */
.suggestions li {
  padding: 5px 10px; /* ✅ Reduce padding */
  cursor: pointer;
  border-bottom: 1px solid #555;
  transition: background-color 0.2s;
  font-size: 0.75rem; /* ✅ Smaller text */
}

/* Hover Effect */
.suggestions li:hover {
  background-color: #222;
  color: #facc15;
}

/* Remove Border from Last Item */
.suggestions li:last-child {
  border-bottom: none;
}

/* Clear input button */
.clear-input {
  cursor: pointer;
  color: #999;
  font-size: 1.2rem;
  display: none;
  padding: 0 5px;
  margin-left: auto;
}

.clear-input:hover {
  color: #666;
}

.banner-slider {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.slider {
  display: flex;
  width: 100%;
}

.slide {
  position: relative;
  width: 100%;
}

.slide img {
  width: 100%;
  height: 100vh;
  object-fit: cover; /* Ensures image covers the section */
}
.hero-banner {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.banner-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center bottom; /* Focus image on bottom area */
}

.banner-content {
  position: absolute;
  top: 8%;
  left: 5%; /* Reduced from 10% to move content left */
  color: black;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  max-width: 600px;
  margin-bottom: 10vh;
  margin-right: 50px;
  transform: translateX(-5px); /* Fine-tune left positioning */
}
.banner-content h1 {
  font-size: 2.5rem; /* Larger heading */
  font-weight: 400; /* Bolder weight */
  margin-bottom: 1.5rem;
  line-height: 1.15;
}

.tagline {
  font-size: 1.5rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  font-weight: 500;
}
.get-started {
  background: #ff6b00;
  color: white;
  border: none;
  padding: 8px 25px; /* Perfect medium size */
  font-size: 1.15rem; /* Slightly larger than base */
  font-weight: 600;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  display: inline-block;
  min-width: 180px; /* Ensures consistent medium width */
  text-align: center;
}

.get-started:hover {
  background: #e05d00;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

/* Reset Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Ubuntu, sans-serif;
}

/* Reset Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Ubuntu, sans-serif;
}

/* Reset styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Ubuntu, sans-serif;
}

/* Services Page Navbar Styles */
:root {
  --primary-color: #152c69;
  --secondary-color: #facc15;
  --accent-color: #d97706;
  --text-color: #333;
  --light-color: #f8f9fa;
  --dark-color: #212529;
  --border-radius: 10px;
  --box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--primary-color);
  padding: 10px 20px;
  color: white;
}

.contact-info {
  font-size: 14px;
}

.social-media a {
  color: white;
  margin-left: 15px;
  font-size: 16px;
  transition: var(--transition);
  text-decoration: none;
}

.social-media a:hover {
  color: var(--secondary-color);
}

/* Navigation Styles */
.navbar {
  background-color: white;
  box-shadow: var(--box-shadow);
  padding: 15px 0;
}

.navbar-brand img {
  max-height: 50px;
}

.navbar-nav .nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 10px 15px;
  transition: var(--transition);
  text-decoration: none;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
  color: var(--primary-color);
}

.login-btn {
  background-color: var(--secondary-color);
  color: var(--dark-color);
  border: none;
  padding: 8px 20px;
  border-radius: 50px;
  font-weight: 600;
  transition: var(--transition);
  text-decoration: none;
}

.login-btn:hover {
  background-color: var(--accent-color);
  color: white;
}

/* Navbar Alignment */
.u-nav-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

/* Navigation Links */
.u-nav {
  list-style: none;
  display: flex;
  gap: 20px;
  padding: 0;
  margin: 0;
}

.u-nav-item a {
  text-decoration: none;
  color: black;
  font-size: 18px;
  font-weight: 500;
  padding: 10px 20px;
  transition: color 0.3s ease-in-out;
}

.u-nav-item a:hover {
  color: #1530fa; /* hover effect */
}

/* Dropdown Styling */
.dropdown {
  position: relative;
  margin-left: auto;
}

.dropdown-btn {
  background-color: #494742;
  color: #f1f1f1;
  font-size: 16px;
  font-weight: bold;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
}

.dropdown-btn:hover {
  background-color: #d97706;
}

/* Dropdown Content */
.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: white;
  min-width: 150px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  border-radius: 5px;
  overflow: hidden;
  z-index: 10;
}

.dropdown-content a {
  display: block;
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  transition: background 0.3s ease-in-out;
}

.dropdown-content a:hover {
  background-color: #c3c3cd;
}

/* Show dropdown on hover */
.dropdown:hover .dropdown-content {
  display: block;
}

/* General Styles */
body {
  font-family: Arial, sans-serif;
  background-color: #f4f4f4;
  margin: 0;
  padding: 0;
}

/* Services Section */
.services-section {
  padding: 50px 20px;
  text-align: center;
  background-color: #fff;
}

.section-title {
  font-size: 2.5rem;
  margin-bottom: 30px;
  color: #333;
}

/* Services Grid */
.services-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

/* Flip Card Container */
.flip-card {
  background-color: transparent;
  width: 350px; /* Increased size */
  height: 400px; /* Increased size */
  perspective: 1200px;
}

/* Flip Card Inner */
.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

/* Flip on Hover */
.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

/* Front & Back Styles */
.flip-card-front,
.flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  text-align: center;
}

/* Front Side */
.flip-card-front {
  background-size: cover;
  background-position: center;
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
}

/* Back Side */
.flip-card-back {
  background: #fff;
  color: #000;
  transform: rotateY(180deg);
  padding: 30px;
}

/* Button */
.flip-card button {
  background-color: #5669f3;
  color: white;
  border: none;
  padding: 12px 18px;
  margin-top: 15px;
  cursor: pointer;
  border-radius: 5px;
  font-size: 1rem;
  transition: background 0.3s;
}

.flip-card button:hover {
  background-color: #020c7d;
}

/* View More Button  */
.view-more-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 50px;
}

.view-more-btn {
  background: linear-gradient(90deg, #0f27b0, #5669f3);
  color: white;
  font-size: 1.2rem;
  padding: 12px 30px;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease-in-out;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.view-more-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
}

.view-more-btn:active {
  transform: translateY(2px);
}

/* Container and Section */
.container {
  width: 90%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 0;
}
.feat {
  background: #fff;
  padding: 40px 0;
}

/* Section Head */
.section-head {
  text-align: center;
  margin-bottom: 40px;
}
.section-head h4 {
  font-size: 32px;
  color: #000;
  margin-bottom: 10px;
  position: relative;
  display: inline-block;
}
.section-head h4 span {
  color: #007bff; /* Blue accent */
}
.section-head h4::after {
  content: "";
  display: block;
  width: 60px;
  height: 3px;
  background: #007bff;
  margin: 8px auto 0;
}
.section-head p {
  font-size: 16px;
  color: #444;
}

/* Row and Cards */
.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.card {
  background: #fff;
  width: calc(25% - 20px);
  margin: 10px;
  padding: 20px;
  text-align: center;
  border: 2px solid #ddd;
  border-radius: 10px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Hover Animation */
.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

/* Card Icon */
.icon {
  font-size: 40px;
  color: #007bff; /* Blue accent */
  margin-bottom: 15px;
  display: inline-block;
}

/* Card Heading and Text */
.card h6 {
  font-size: 20px;
  color: #000; /* Black text */
  margin-bottom: 10px;
}
.card p {
  font-size: 14px;
  color: #333;
  line-height: 1.6;
}

/* Responsive */
@media (max-width: 992px) {
  .card {
    width: calc(50% - 20px);
  }
}
@media (max-width: 600px) {
  .card {
    width: 100%;
    margin: 10px 0;
  }
}

.header-design {
  background: linear-gradient(326deg, #1e3a8a 0%, #2563eb 74%);
  height: 120px;
  text-align: center;
  position: relative;
}

.parcel-info {
  color: white;
  font-size: 20px;
  font-weight: bold;
  padding-top: 40px;
}

.footer-wave {
  width: 100%;
  height: 120px;
  background: url("https://1.bp.blogspot.com/-NYl6L8pz8B4/XoIVXwfhlNI/AAAAAAAAU3k/nxJKiLT706Mb7jUFiM5vdCsOSNnFAh0yQCLcBGAsYHQ/s1600/hero-wave.png")
    repeat-x;
  position: absolute;
  bottom: -40px;
  left: 0;
  animation: wave 4s linear infinite;
}

@keyframes wave {
  0% {
    background-position: 0;
  }
  100% {
    background-position: -1440px;
  }
}

.container {
  padding: 50px 0;
}

.row {
  display: flex;
  justify-content: center;
  gap: 50px;
  flex-wrap: wrap;
}

.feature-item {
  background: white;
  padding: 20px;
  border-radius: 10px;
  width: 300px;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.1);
}

.feature-icon img {
  width: 80px;
  height: 80px;
}

h3 {
  margin: 15px 0;
  font-size: 20px;
  color: #1e3a8a;
}

p {
  font-size: 14px;
  color: #555;
}

/* Header Section */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ccc0c0;
  padding: 10px 20px;
  font-size: 14px;
}

.contact-info {
  color: rgb(0, 0, 0);
}

.social-media a {
  margin-left: 15px;
  text-decoration: none;
  color: rgb(0, 0, 0);
  font-size: 18px;
  transition: color 0.3s ease-in-out;
}

.social-media a:hover {
  color: lightblue;
}

/* Login Button */
.contact-btn {
  background-color: #4d4d4d; /* Yellow button */
  color: #f3f3f3; /* white text */
  font-size: 16px;
  font-weight: bold;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s ease-in-out;
}

.contact-btn:hover {
  background-color: #1e3a8a; /* Darker Blue */
}

#header {
  height: 60px; /* Adjust height as needed */
  padding: 5px 20px; /* Reduce padding for compact spacing */
  display: flex;
  align-items: center; /* Align items vertically */
  justify-content: space-between;
}

.u-sheet-1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.u-logo-image-1 {
  max-height: 50px; /* Adjust logo size */
}

.u-menu-1 {
  display: flex;
  align-items: center;
  font-size: 0.9rem; /* Adjust text size */
}

.u-nav-container ul {
  display: flex;
  align-items: center;
}

.u-nav-item a {
  padding: 15px 30px; /* Adjust spacing inside menu items */
  line-height: normal; /* 0Ensure proper vertical alignment */
}

.menu-collapse {
  display: flex;
  align-items: center;
}

.u-button-style {
  display: flex;
  align-items: center;
}
.banner-slider {
  position: relative;
  width: 100%;
  max-height: 1000px;
  overflow: hidden;
}

.slider {
  display: flex;
  transition: transform 0.5s ease-in-out;
}

.slide {
  min-width: 100%;
  text-align: center;
  position: relative;
}

.slide img {
  width: 100%;
  height: auto;
}

.slide h1 {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px 20px;
  border-radius: 5px;
}

.prev,
.next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  padding: 10px;
  cursor: pointer;
}

.prev {
  left: 10px;
}
.next {
  right: 10px;
}

.u-section-1 {
  background-image: none;
  min-height: 1009px;
}

.u-section-1 .u-image-1 {
  min-height: 653px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.3),
      rgba(0, 0, 0, 0.3)
    ),
    url("images/banner-2.png");
  background-position: 50% 50%;
}

.u-section-1 .u-container-layout-1 {
  padding: 210px 30px 30px;
}

.u-section-1 .u-text-1 {
  font-weight: 700;
  margin: 0 auto;
}

.u-section-1 .u-layout-wrap-1 {
  width: 1140px;
  margin: -210px auto 60px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 92px;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 424px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --radius: 50px;
}

.u-section-1 .u-container-layout-3 {
  padding: 30px 20px;
}

.u-section-1 .u-icon-1 {
  height: 93px;
  width: 93px;
  margin: 0 auto;
  padding: 19px;
}

.u-section-1 .u-text-2 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-3 {
  font-size: 1rem;
  font-style: normal;
  margin: 20px 0 0;
}

.u-section-1 .u-btn-1 {
  border-style: solid;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 0.875rem;
  margin: 20px auto 0;
  padding: 0;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 421px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --radius: 50px;
}

.u-section-1 .u-container-layout-4 {
  padding: 30px 20px;
}

.u-section-1 .u-icon-2 {
  height: 93px;
  width: 93px;
  margin: 0 auto;
  padding: 19px;
}

.u-section-1 .u-text-4 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-5 {
  margin: 20px 1px 0;
}

.u-section-1 .u-btn-2 {
  border-style: solid;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 0.875rem;
  margin: 20px auto 0;
  padding: 0;
}

.u-section-1 .u-layout-cell-4 {
  min-height: 95px;
}

.u-section-1 .u-container-layout-5 {
  padding: 30px;
}

.u-section-1 .u-layout-cell-5 {
  min-height: 95px;
}

.u-section-1 .u-container-layout-6 {
  padding: 30px;
}

.u-section-1 .u-layout-cell-6 {
  min-height: 441px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --radius: 50px;
}

.u-section-1 .u-container-layout-7 {
  padding: 30px 20px;
}

.u-section-1 .u-icon-3 {
  height: 93px;
  width: 93px;
  margin: 0 auto;
  padding: 19px;
}

.u-section-1 .u-text-6 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-7 {
  font-style: normal;
  margin: 20px 1px 0;
}

.u-section-1 .u-btn-3 {
  border-style: solid;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 0.875rem;
  margin: 20px auto 0;
  padding: 0;
}

.u-section-1 .u-layout-cell-7 {
  min-height: 436px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  --radius: 50px;
}

.u-section-1 .u-container-layout-8 {
  padding: 30px 20px;
}

.u-section-1 .u-icon-4 {
  height: 93px;
  width: 93px;
  background-image: none;
  color: rgb(0, 0, 0) !important;
  margin: 0 auto;
  padding: 19px;
}

.u-section-1 .u-text-8 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-9 {
  margin: 20px 0 0;
}

.u-section-1 .u-btn-4 {
  border-style: solid;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 0.875rem;
  margin: 20px auto 0;
  padding: 0;
}

.u-section-1 .u-layout-cell-8 {
  min-height: 99px;
}

.u-section-1 .u-container-layout-9 {
  padding: 30px;
}

@media (max-width: 1199px) {
  .u-section-1 {
    min-height: 918px;
  }

  .u-section-1 .u-image-1 {
    height: auto;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 940px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 76px;
  }

  .u-section-1 .u-layout-cell-2 {
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
    min-height: 350px;
  }

  .u-section-1 .u-layout-cell-3 {
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
    min-height: 347px;
  }

  .u-section-1 .u-text-5 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 78px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 78px;
  }

  .u-section-1 .u-layout-cell-6 {
    --animation-custom_in-opacity: 0;
    --animation-custom_in-rotate: 0deg;
    --animation-custom_in-scale: 1;
    min-height: 364px;
  }

  .u-section-1 .u-text-7 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-7 {
    min-height: 360px;
  }

  .u-section-1 .u-layout-cell-8 {
    min-height: 82px;
  }
}

@media (max-width: 991px) {
  .u-section-1 {
    min-height: 1542px;
  }

  .u-section-1 .u-image-1 {
    background-position: 9.07% 50%;
  }

  .u-section-1 .u-container-layout-1 {
    padding-top: 180px;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 720px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 116px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 119px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 119px;
  }

  .u-section-1 .u-layout-cell-6 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-7 {
    min-height: 100px;
  }

  .u-section-1 .u-icon-4 {
    margin-top: -297px;
  }

  .u-section-1 .u-layout-cell-8 {
    min-height: 126px;
  }
}

@media (max-width: 767px) {
  .u-section-1 {
    min-height: 1939px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-top: 160px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 540px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 174px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 179px;
  }

  .u-section-1 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 179px;
  }

  .u-section-1 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-7 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-1 .u-container-layout-8 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-1 .u-icon-4 {
    margin-top: 0;
  }

  .u-section-1 .u-layout-cell-8 {
    min-height: 189px;
  }

  .u-section-1 .u-container-layout-9 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 {
    min-height: 1967px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-top: 155px;
  }

  .u-section-1 .u-layout-wrap-1 {
    width: 340px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 110px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 113px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 113px;
  }

  .u-section-1 .u-container-layout-7 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-1 .u-container-layout-8 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-1 .u-icon-4 {
    margin-top: 1px;
  }

  .u-section-1 .u-layout-cell-8 {
    min-height: 119px;
  }
}
.u-section-2 {
  background-image: none;
}

.u-section-2 .u-sheet-1 {
  min-height: 695px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 555px;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px 29px 30px 0;
}

.u-section-2 .u-text-1 {
  margin: 0;
}

.u-section-2 .u-text-2 {
  padding-left: 3px;
  line-height: 2;
  margin: 40px 0 0;
}

.u-section-2 .u-btn-1 {
  border-style: solid;
  background-image: none;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0px;
  font-size: 1.25rem;
  margin: 30px auto 0 0;
  padding: 18px 36px 19px;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 555px;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px;
}

.u-section-2 .u-image-1 {
  width: 432px;
  height: 495px;
  --radius: 50px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 180deg;
  --animation-custom_in-scale: 1;
  margin: 0 auto;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 631px;
  }

  .u-section-2 .u-layout-wrap-1 {
    --radius: 50px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 458px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 137px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 458px;
  }

  .u-section-2 .u-image-1 {
    width: 410px;
    height: 470px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 632px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 27px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 351px;
  }

  .u-section-2 .u-image-1 {
    width: 300px;
    height: 344px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 995px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-2 .u-text-1 {
    width: auto;
    margin-right: 56px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 527px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 1044px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 332px;
  }
}
.u-section-3 .u-sheet-1 {
  min-height: 1232px;
}

.u-section-3 .u-text-1 {
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  font-size: 3.4375rem;
  margin: 60px auto 0;
}

.u-section-3 .u-list-1 {
  margin-top: 55px;
  margin-bottom: 60px;
  grid-template-rows: repeat(1, auto);
}

.u-section-3 .u-repeater-1 {
  grid-template-columns: repeat(3, calc(33.3333% - 20px));
  min-height: 976px;
  grid-gap: 30px;
}

.u-section-3 .u-image-1 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1487017159836-4e23ece2e4cf.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-1 {
  padding: 30px;
}

.u-section-3 .u-line-1 {
  width: 276px;
  transform-origin: left center;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-2 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
}

.u-section-3 .u-text-3 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

.u-section-3 .u-image-2 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1581092162384-8987c1d64718.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-2 {
  padding: 30px;
}

.u-section-3 .u-line-2 {
  width: 276px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-4 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
  font-size: 1.5rem;
  font-weight: 500;
}

.u-section-3 .u-text-5 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

.u-section-3 .u-image-3 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1486312338219-ce68d2c6f44d.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-3 {
  padding: 30px;
}

.u-section-3 .u-line-3 {
  width: 276px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-6 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
  font-size: 1.5rem;
  font-weight: 500;
}

.u-section-3 .u-text-7 {
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

.u-section-3 .u-image-4 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1494083306499-e22e4a457632.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-4 {
  padding: 30px;
}

.u-section-3 .u-line-4 {
  width: 276px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-8 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
  font-size: 1.5rem;
  font-weight: 500;
}

.u-section-3 .u-text-9 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

.u-section-3 .u-image-5 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1473091534298-04dcbce3278c.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-5 {
  padding: 30px;
}

.u-section-3 .u-line-5 {
  width: 276px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-10 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
  font-size: 1.5rem;
  font-weight: 500;
}

.u-section-3 .u-text-11 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

.u-section-3 .u-image-6 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.25),
      rgba(0, 0, 0, 0.25)
    ),
    url("images/photo-1531297484001-80022131f5a1.jpeg");
  background-position: 50% 50%;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-3 .u-container-layout-6 {
  padding: 30px;
}

.u-section-3 .u-line-6 {
  width: 276px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto 0 0;
}

.u-section-3 .u-text-12 {
  margin-left: 0;
  margin-bottom: 0;
  margin-top: 20px;
  --animation-custom_in-scale: 0.3;
  text-shadow: 0px 0px 8px rgba(128, 128, 128, 1);
  font-size: 1.5rem;
  font-weight: 500;
}

.u-section-3 .u-text-13 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 12px auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1056px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(3, calc(33.333333333333336% - 20px));
    min-height: 801px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 15px));
    min-height: 1420px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-text-1 {
    font-size: 2.25rem;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2676px;
  }

  .u-section-3 .u-text-1 {
    font-size: 1.875rem;
    margin-top: 11px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: 11px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 2467px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-left: 20px;
    padding-right: 20px;
  }
}
.u-section-4 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.4)
    ),
    url("images/photo-1489486501123-5c1af10d0be6.jpeg");
}

.u-section-4 .u-sheet-1 {
  min-height: 705px;
}

.u-section-4 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: -300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 60px auto 0;
}

.u-section-4 .u-text-2 {
  font-size: 1.25rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  width: 747px;
  margin: 30px auto 0;
}

.u-section-4 .u-btn-1 {
  border-style: solid;
  background-image: none;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0px;
  font-size: 1.25rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 40px auto 60px;
  padding: 18px 36px 19px;
}

@media (max-width: 1199px) {
  .u-section-4 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-sheet-1 {
    min-height: 581px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 445px;
  }

  .u-section-4 .u-text-2 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 334px;
  }

  .u-section-4 .u-text-2 {
    font-size: 1rem;
    width: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 210px;
  }

  .u-section-4 .u-text-2 {
    width: 340px;
  }
}
.u-section-5 {
  min-height: 100px;
}

.u-section-5 .u-sheet-1 {
  min-height: 684px;
}

.u-section-5 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
  margin-left: auto;
}

.u-section-5 .u-image-1 {
  min-height: 594px;
  background-image: url("images/photo-1460925895917-afdab827c52f.jpeg");
  --radius: 50px;
  background-position: 50% 50%;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-5 .u-container-layout-1 {
  padding: 0 23px;
}

.u-section-5 .u-layout-cell-2 {
  min-height: 594px;
  --radius: 50px;
}

.u-section-5 .u-container-layout-2 {
  padding: 30px 50px;
}

.u-section-5 .u-text-1 {
  margin: 0;
}

.u-section-5 .u-text-2 {
  margin: 30px 0 0;
}

.u-section-5 .u-list-1 {
  margin-top: 63px;
  margin-bottom: 0;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: 33.3333%;
  grid-template-columns: repeat(3, 33.3333%);
  min-height: 144px;
  grid-gap: 0px;
}

.u-section-5 .u-container-layout-3 {
  padding: 10px 20px;
}

.u-section-5 .u-text-3 {
  font-weight: 700;
  margin: 0;
}

.u-section-5 .u-text-4 {
  font-weight: 400;
  text-transform: uppercase;
  font-size: 1rem;
  margin: 20px 0 0;
}

.u-section-5 .u-container-layout-4 {
  padding: 10px 20px;
}

.u-section-5 .u-text-5 {
  font-weight: 700;
  margin: 0;
}

.u-section-5 .u-text-6 {
  font-weight: 400;
  text-transform: uppercase;
  font-size: 1rem;
  margin: 20px 0 0;
}

.u-section-5 .u-container-layout-5 {
  padding: 10px 20px;
}

.u-section-5 .u-text-7 {
  font-weight: 700;
  margin: 0;
}

.u-section-5 .u-text-8 {
  font-weight: 400;
  text-transform: uppercase;
  font-size: 1rem;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-5 {
    min-height: 489px;
  }

  .u-section-5 .u-sheet-1 {
    min-height: 580px;
  }

  .u-section-5 .u-layout-wrap-1 {
    position: relative;
    margin-left: initial;
  }

  .u-section-5 .u-image-1 {
    min-height: 490px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 490px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 33.333333333333336%;
    grid-template-columns: repeat(3, 33.333333333333336%);
    min-height: 112px;
  }
}

@media (max-width: 991px) {
  .u-section-5 {
    min-height: 100px;
  }

  .u-section-5 .u-sheet-1 {
    min-height: 1260px;
  }

  .u-section-5 .u-image-1 {
    min-height: 712px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 430px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 33.3333%;
    grid-template-columns: repeat(3, 33.3333%);
    min-height: 136px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 323px;
  }

  .u-section-5 .u-image-1 {
    min-height: 527px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 518px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 50%;
    grid-template-columns: repeat(2, 50%);
    min-height: 247px;
  }

  .u-section-5 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-image-1 {
    min-height: 332px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 100%;
    grid-template-columns: 100%;
  }
}
.u-section-6 {
  background-image: none;
}

.u-section-6 .u-sheet-1 {
  min-height: 572px;
}

.u-section-6 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 60px auto 0;
}

.u-section-6 .u-list-1 {
  margin-top: 64px;
  margin-bottom: 60px;
}

.u-section-6 .u-repeater-1 {
  grid-template-columns: repeat(3, calc(33.3333% - 20px));
  min-height: 332px;
  grid-gap: 30px;
}

.u-section-6 .u-list-item-1 {
  background-image: none;
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-1 {
  padding: 30px;
}

.u-section-6 .u-text-2 {
  font-weight: 700;
  margin: 0;
}

.u-section-6 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-6 .u-line-1 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-6 .u-text-4 {
  text-transform: uppercase;
  font-weight: 400;
  margin: 30px 0 0;
}

.u-section-6 .u-list-item-2 {
  background-image: none;
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-2 {
  padding: 30px;
}

.u-section-6 .u-text-5 {
  font-weight: 700;
  margin: 0;
}

.u-section-6 .u-text-6 {
  margin: 20px 0 0;
}

.u-section-6 .u-line-2 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-6 .u-text-7 {
  text-transform: uppercase;
  font-weight: 400;
  font-size: 1.125rem;
  margin: 30px 0 0;
}

.u-section-6 .u-list-item-3 {
  background-image: none;
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-6 .u-container-layout-3 {
  padding: 30px;
}

.u-section-6 .u-text-8 {
  font-weight: 700;
  margin: 0;
}

.u-section-6 .u-text-9 {
  margin: 20px 0 0;
}

.u-section-6 .u-line-3 {
  margin-top: 30px;
  margin-bottom: 0;
  transform-origin: left center;
}

.u-section-6 .u-text-10 {
  text-transform: uppercase;
  font-weight: 400;
  margin: 30px 0 0;
}

@media (max-width: 1199px) {
  .u-section-6 .u-repeater-1 {
    grid-template-columns: repeat(3, calc(33.333333333333336% - 20px));
    min-height: 270px;
  }
}

@media (max-width: 991px) {
  .u-section-6 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 15px));
    min-height: 665px;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-6 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-6 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-6 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }
}
.u-section-7 {
  background-image: none;
  min-height: 1242px;
}

.u-section-7 .u-group-1 {
  min-height: 554px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-7 .u-container-layout-1 {
  padding: 90px 30px 30px;
}

.u-section-7 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 0 auto;
}

.u-section-7 .u-text-2 {
  font-size: 1.25rem;
  width: 851px;
  margin: 30px auto 0;
}

.u-section-7 .u-gallery-1 {
  height: 745px;
  width: 1140px;
  grid-gap: 17px;
  margin: -256px auto 0;
}

.u-section-7 .u-gallery-inner-1 {
  grid-template-columns: repeat(3, auto);
  grid-gap: 41px;
}

.u-section-7 .u-over-slide-1 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-over-slide-2 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-over-slide-3 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-over-slide-4 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-over-slide-5 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-over-slide-6 {
  background-image: linear-gradient(
    0deg,
    rgba(0, 0, 0, 0.2),
    rgba(0, 0, 0, 0.2)
  );
  padding: 20px;
}

.u-section-7 .u-btn-1 {
  border-style: solid;
  font-weight: 700;
  text-transform: uppercase;
  font-size: 1.25rem;
  letter-spacing: 0px;
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 40px auto 60px;
  padding: 20px 61px 20px 60px;
}

@media (max-width: 1199px) {
  .u-section-7 {
    min-height: 1110px;
  }

  .u-section-7 .u-group-1 {
    height: auto;
  }

  .u-section-7 .u-gallery-1 {
    height: 613px;
    width: 940px;
  }
}

@media (max-width: 991px) {
  .u-section-7 {
    min-height: 1562px;
  }

  .u-section-7 .u-container-layout-1 {
    padding-top: 80px;
  }

  .u-section-7 .u-text-2 {
    width: 660px;
  }

  .u-section-7 .u-gallery-1 {
    height: 1075px;
    width: 720px;
    margin-top: -227px;
  }

  .u-section-7 .u-gallery-inner-1 {
    grid-template-columns: repeat(2, auto);
  }
}

@media (max-width: 767px) {
  .u-section-7 {
    min-height: 3771px;
  }

  .u-section-7 .u-container-layout-1 {
    padding-top: 85px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-7 .u-text-2 {
    font-size: 1rem;
    width: 520px;
  }

  .u-section-7 .u-gallery-1 {
    height: 3284px;
    width: 540px;
    margin-top: -247px;
  }

  .u-section-7 .u-gallery-inner-1 {
    grid-template-columns: repeat(1, auto);
  }
}

@media (max-width: 575px) {
  .u-section-7 {
    min-height: 2586px;
  }

  .u-section-7 .u-group-1 {
    min-height: 509px;
  }

  .u-section-7 .u-container-layout-1 {
    padding-top: 80px;
  }

  .u-section-7 .u-text-2 {
    width: 320px;
  }

  .u-section-7 .u-gallery-1 {
    height: 2099px;
    width: 340px;
    margin-top: -202px;
  }
}
.u-section-8 {
  background-image: linear-gradient(
      0deg,
      rgba(0, 0, 0, 0.4),
      rgba(0, 0, 0, 0.4)
    ),
    url("images/photo-1720983590448-28b749bd403d.jpeg");
}

.u-section-8 .u-sheet-1 {
  min-height: 705px;
}

.u-section-8 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: -300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 60px auto 0;
}

.u-section-8 .u-text-2 {
  font-size: 1.25rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  width: 747px;
  margin: 30px auto 0;
}

.u-section-8 .u-btn-1 {
  border-style: solid;
  background-image: none;
  text-transform: uppercase;
  font-weight: 700;
  letter-spacing: 0px;
  font-size: 1.25rem;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 40px auto 60px;
  padding: 18px 53px 19px 52px;
}

@media (max-width: 1199px) {
  .u-section-8 {
    background-position: 50% 50%;
  }

  .u-section-8 .u-sheet-1 {
    min-height: 581px;
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-sheet-1 {
    min-height: 445px;
  }

  .u-section-8 .u-text-2 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-sheet-1 {
    min-height: 334px;
  }

  .u-section-8 .u-text-2 {
    font-size: 1rem;
    width: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-8 .u-sheet-1 {
    min-height: 210px;
  }

  .u-section-8 .u-text-2 {
    width: 340px;
  }
}
.u-section-9 .u-sheet-1 {
  min-height: 691px;
}

.u-section-9 .u-layout-wrap-1 {
  width: 1140px;
  margin: 60px 0;
}

.u-section-9 .u-layout-cell-1 {
  min-height: 601px;
  --animation-custom_in-translate_x: -300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-9 .u-container-layout-1 {
  padding: 30px 0;
}

.u-section-9 .u-text-1 {
  margin: 0;
}

.u-section-9 .u-list-1 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-9 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 375px;
  grid-gap: 10px;
}

.u-section-9 .u-container-layout-2 {
  padding: 10px 20px 10px 0;
}

.u-section-9 .u-text-2 {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 1.5rem;
}

.u-section-9 .u-line-1 {
  transform-origin: left center;
  width: 258px;
  margin: 20px 0 0;
}

.u-section-9 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-9 .u-container-layout-3 {
  padding: 10px 20px 10px 0;
}

.u-section-9 .u-text-4 {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 1.5rem;
}

.u-section-9 .u-line-2 {
  width: 258px;
  transform-origin: left center;
  margin: 20px 0 0;
}

.u-section-9 .u-text-5 {
  margin: 20px 0 0;
}

.u-section-9 .u-container-layout-4 {
  padding: 10px 20px 10px 0;
}

.u-section-9 .u-text-6 {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 1.5rem;
}

.u-section-9 .u-line-3 {
  width: 258px;
  transform-origin: left center;
  margin: 20px 0 0;
}

.u-section-9 .u-text-7 {
  margin: 20px 0 0;
}

.u-section-9 .u-container-layout-5 {
  padding: 10px 20px 10px 0;
}

.u-section-9 .u-text-8 {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 1.5rem;
}

.u-section-9 .u-line-4 {
  width: 258px;
  transform-origin: left center;
  margin: 20px 0 0;
}

.u-section-9 .u-text-9 {
  margin: 20px 0 0;
}

.u-section-9 .u-layout-cell-2 {
  min-height: 601px;
}

.u-section-9 .u-container-layout-6 {
  padding: 30px 0;
}

.u-section-9 .u-image-1 {
  width: 502px;
  height: 511px;
  --radius: 50px;
  --animation-custom_in-translate_x: 300px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 180deg;
  --animation-custom_in-scale: 1;
  margin: 0 auto;
}

@media (max-width: 1199px) {
  .u-section-9 .u-sheet-1 {
    min-height: 586px;
  }

  .u-section-9 .u-layout-wrap-1 {
    width: 940px;
  }

  .u-section-9 .u-layout-cell-1 {
    min-height: 496px;
  }

  .u-section-9 .u-repeater-1 {
    min-height: 308px;
  }

  .u-section-9 .u-text-2 {
    font-size: 1.25rem;
  }

  .u-section-9 .u-line-1 {
    width: 202px;
    margin-right: 17px;
  }

  .u-section-9 .u-text-4 {
    font-size: 1.25rem;
  }

  .u-section-9 .u-line-2 {
    width: 202px;
    margin-right: 17px;
  }

  .u-section-9 .u-text-6 {
    font-size: 1.25rem;
  }

  .u-section-9 .u-line-3 {
    width: 202px;
    margin-right: 17px;
  }

  .u-section-9 .u-text-8 {
    font-size: 1.25rem;
  }

  .u-section-9 .u-line-4 {
    width: 202px;
    margin-right: 17px;
  }

  .u-section-9 .u-layout-cell-2 {
    min-height: 563px;
  }

  .u-section-9 .u-image-1 {
    width: 395px;
    height: 463px;
    margin-top: 5px;
  }
}

@media (max-width: 991px) {
  .u-section-9 .u-sheet-1 {
    min-height: 470px;
  }

  .u-section-9 .u-layout-wrap-1 {
    width: 720px;
  }

  .u-section-9 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-9 .u-container-layout-1 {
    padding-right: 66px;
  }

  .u-section-9 .u-line-1 {
    margin-right: auto;
  }

  .u-section-9 .u-line-2 {
    margin-right: auto;
  }

  .u-section-9 .u-line-3 {
    margin-right: auto;
  }

  .u-section-9 .u-line-4 {
    margin-right: auto;
  }

  .u-section-9 .u-layout-cell-2 {
    min-height: 499px;
  }

  .u-section-9 .u-image-1 {
    width: 690px;
    height: 404px;
  }
}

@media (max-width: 767px) {
  .u-section-9 .u-sheet-1 {
    min-height: 760px;
  }

  .u-section-9 .u-layout-wrap-1 {
    width: 540px;
  }

  .u-section-9 .u-container-layout-1 {
    padding-right: 70px;
  }

  .u-section-9 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-9 .u-container-layout-2 {
    padding-right: 10px;
    padding-bottom: 30px;
  }

  .u-section-9 .u-text-2 {
    font-size: 1.5rem;
  }

  .u-section-9 .u-line-1 {
    width: 441px;
  }

  .u-section-9 .u-container-layout-3 {
    padding-right: 10px;
    padding-bottom: 30px;
  }

  .u-section-9 .u-text-4 {
    font-size: 1.5rem;
  }

  .u-section-9 .u-line-2 {
    width: 441px;
  }

  .u-section-9 .u-container-layout-4 {
    padding-right: 10px;
    padding-bottom: 30px;
  }

  .u-section-9 .u-text-6 {
    font-size: 1.5rem;
  }

  .u-section-9 .u-line-3 {
    width: 441px;
  }

  .u-section-9 .u-container-layout-5 {
    padding-right: 10px;
    padding-bottom: 30px;
  }

  .u-section-9 .u-text-8 {
    font-size: 1.5rem;
  }

  .u-section-9 .u-line-4 {
    width: 441px;
  }

  .u-section-9 .u-layout-cell-2 {
    min-height: 619px;
  }

  .u-section-9 .u-image-1 {
    height: 476px;
    margin-top: 0;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }
}

@media (max-width: 575px) {
  .u-section-9 .u-sheet-1 {
    min-height: 549px;
  }

  .u-section-9 .u-layout-wrap-1 {
    width: 340px;
  }

  .u-section-9 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-9 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-9 .u-line-1 {
    width: 320px;
  }

  .u-section-9 .u-line-2 {
    width: 320px;
  }

  .u-section-9 .u-line-3 {
    width: 320px;
  }

  .u-section-9 .u-line-4 {
    width: 320px;
  }

  .u-section-9 .u-layout-cell-2 {
    min-height: 390px;
  }

  .u-section-9 .u-image-1 {
    height: 300px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }
}
.u-section-10 {
  background-image: none;
}

.u-section-10 .u-sheet-1 {
  min-height: 778px;
}

.u-section-10 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 60px auto 0;
}

.u-section-10 .u-text-2 {
  font-size: 1.23529rem;
  font-weight: 400;
  line-height: 1.80001;
  width: 760px;
  margin: 20px auto 0;
}

.u-section-10 .u-list-1 {
  margin-top: 40px;
  margin-bottom: 0;
}

.u-section-10 .u-repeater-1 {
  grid-template-columns: repeat(3, calc(33.3333% - 20px));
  min-height: 424px;
  grid-gap: 30px;
}

.u-section-10 .u-list-item-1 {
  background-image: none;
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-10 .u-container-layout-1 {
  padding: 30px;
}

.u-section-10 .u-image-1 {
  height: 274px;
  --radius: 40px;
  margin: 0;
}

.u-section-10 .u-text-3 {
  margin: 13px 0 0;
}

.u-section-10 .u-text-4 {
  font-weight: 400;
  margin: 10px 0 0;
}

.u-section-10 .u-list-item-2 {
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-10 .u-container-layout-2 {
  padding: 30px;
}

.u-section-10 .u-image-2 {
  height: 274px;
  --radius: 40px;
  margin: 0;
}

.u-section-10 .u-text-5 {
  font-size: 1.5rem;
  text-transform: none;
  font-weight: 500;
  font-family: Ubuntu, sans-serif;
  margin: 13px 0 0;
}

.u-section-10 .u-text-6 {
  font-weight: 400;
  text-transform: none;
  font-size: 1.125rem;
  letter-spacing: normal;
  margin: 10px 0 0;
}

.u-section-10 .u-list-item-3 {
  --radius: 50px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
}

.u-section-10 .u-container-layout-3 {
  padding: 30px;
}

.u-section-10 .u-image-3 {
  height: 274px;
  --radius: 40px;
  margin: 0;
}

.u-section-10 .u-text-7 {
  font-size: 1.5rem;
  text-transform: none;
  font-weight: 500;
  font-family: Ubuntu, sans-serif;
  margin: 13px 0 0;
}

.u-section-10 .u-text-8 {
  font-weight: 400;
  text-transform: none;
  font-size: 1.125rem;
  letter-spacing: normal;
  margin: 10px 0 0;
}

.u-section-10 .u-text-9 {
  text-align: left;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 300px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 20px auto 60px;
}

@media (max-width: 1199px) {
  .u-section-10 .u-sheet-1 {
    min-height: 714px;
  }

  .u-section-10 .u-repeater-1 {
    grid-template-columns: repeat(3, calc(33.333333333333336% - 20px));
    min-height: 345px;
  }

  .u-section-10 .u-image-1 {
    height: 217px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-2 {
    height: 217px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-3 {
    height: 217px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 991px) {
  .u-section-10 .u-text-2 {
    width: 720px;
  }

  .u-section-10 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 15px));
    min-height: 842px;
  }

  .u-section-10 .u-image-1 {
    height: 257px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-2 {
    height: 257px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-3 {
    height: 257px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 767px) {
  .u-section-10 .u-text-2 {
    font-size: 1rem;
    width: 540px;
  }

  .u-section-10 .u-repeater-1 {
    grid-template-columns: 100%;
  }

  .u-section-10 .u-image-1 {
    height: 445px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-2 {
    height: 445px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-3 {
    height: 445px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 575px) {
  .u-section-10 .u-text-2 {
    width: 340px;
  }

  .u-section-10 .u-image-1 {
    height: 297px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-2 {
    height: 297px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-10 .u-image-3 {
    height: 297px;
    margin-right: initial;
    margin-left: initial;
  }
}
.u-section-11 {
  background-image: none;
}

.u-section-11 .u-sheet-1 {
  min-height: 670px;
}

.u-section-11 .u-text-1 {
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 60px auto 0;
}

.u-section-11 .u-text-2 {
  width: 894px;
  font-style: normal;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 0.3;
  margin: 20px auto 0;
}

.u-section-11 .u-accordion-1 {
  margin: 30px auto 60px;
}

.u-section-11 .u-accordion-link-1 {
  background-image: none;
  font-weight: 700;
  --radius: 50px;
  padding: 25px 30px;
}

.u-section-11 .u-icon-1 {
  height: 17px;
  width: 17px;
}

.u-section-11 .u-accordion-pane-1 {
  min-height: 154px;
  background-image: none;
}

.u-section-11 .u-container-layout-1 {
  padding: 20px 30px;
}

.u-section-11 .u-accordion-link-2 {
  background-image: none;
  font-weight: 700;
  --radius: 50px;
  padding: 25px 30px;
}

.u-section-11 .u-icon-2 {
  height: 17px;
  width: 17px;
}

.u-section-11 .u-accordion-pane-2 {
  min-height: 154px;
  background-image: none;
}

.u-section-11 .u-container-layout-2 {
  padding: 20px 30px;
}

.u-section-11 .u-accordion-link-3 {
  background-image: none;
  font-weight: 700;
  --radius: 50px;
  padding: 25px 30px;
}

.u-section-11 .u-icon-3 {
  height: 17px;
  width: 17px;
}

.u-section-11 .u-accordion-pane-3 {
  min-height: 154px;
  background-image: none;
}

.u-section-11 .u-container-layout-3 {
  padding: 20px 30px;
}

.u-section-11 .u-accordion-link-4 {
  background-image: none;
  font-weight: 700;
  --radius: 50px;
  padding: 25px 30px;
}

.u-section-11 .u-icon-4 {
  height: 17px;
  width: 17px;
}

.u-section-11 .u-accordion-pane-4 {
  min-height: 154px;
  background-image: none;
}

.u-section-11 .u-container-layout-4 {
  padding: 20px 30px;
}

.u-section-11 .u-accordion-link-5 {
  background-image: none;
  margin-bottom: 0;
  font-weight: 700;
  --radius: 50px;
  padding: 25px 30px;
}

.u-section-11 .u-icon-5 {
  height: 17px;
  width: 17px;
}

.u-section-11 .u-accordion-pane-5 {
  min-height: 154px;
  background-image: none;
}

.u-section-11 .u-container-layout-5 {
  padding: 20px 30px;
}

.u-section-11 .u-text-7 {
  margin-bottom: 0;
}

@media (max-width: 1199px) {
  .u-section-11 .u-accordion-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-11 .u-accordion-pane-1 {
    height: auto;
  }

  .u-section-11 .u-accordion-pane-2 {
    height: auto;
  }

  .u-section-11 .u-accordion-pane-3 {
    height: auto;
  }

  .u-section-11 .u-accordion-pane-4 {
    height: auto;
  }

  .u-section-11 .u-accordion-pane-5 {
    height: auto;
  }
}

@media (max-width: 991px) {
  .u-section-11 .u-text-2 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-11 .u-text-2 {
    width: 540px;
  }

  .u-section-11 .u-container-layout-1 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-11 .u-container-layout-2 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-11 .u-container-layout-3 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-11 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-11 .u-container-layout-5 {
    padding-left: 20px;
    padding-right: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-11 .u-text-2 {
    width: 340px;
  }
}
.u-section-12 .u-sheet-1 {
  min-height: 818px;
}

.u-section-12 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
}

.u-section-12 .u-layout-cell-1 {
  min-height: 698px;
}

.u-section-12 .u-container-layout-1 {
  padding: 0 0 30px;
}

.u-section-12 .u-text-1 {
  margin: 0;
}

.u-section-12 .u-form-1 {
  background-image: none;
  height: 494px;
  margin: 30px 30px 0 0;
}

.u-section-12 .u-btn-1 {
  background-image: none;
  font-size: 1.25rem;
  text-transform: uppercase;
  font-weight: 700;
  padding: 10px 43px 11px 42px;
}

.u-section-12 .u-layout-cell-2 {
  min-height: 698px;
}

.u-section-12 .u-container-layout-2 {
  padding: 0 0 30px;
}

.u-section-12 .u-image-1 {
  width: 569px;
  height: 616px;
  --radius: 50px;
  margin: 0 auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-12 .u-sheet-1 {
    min-height: 696px;
  }

  .u-section-12 .u-layout-cell-1 {
    min-height: 576px;
  }

  .u-section-12 .u-form-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 576px;
  }

  .u-section-12 .u-image-1 {
    width: 487px;
    height: 527px;
  }
}

@media (max-width: 991px) {
  .u-section-12 .u-sheet-1 {
    min-height: 947px;
  }

  .u-section-12 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 827px;
  }

  .u-section-12 .u-image-1 {
    width: 690px;
    height: 690px;
  }
}

@media (max-width: 767px) {
  .u-section-12 .u-sheet-1 {
    min-height: 840px;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 620px;
  }

  .u-section-12 .u-image-1 {
    width: 540px;
    height: 540px;
  }
}

@media (max-width: 575px) {
  .u-section-12 .u-sheet-1 {
    min-height: 610px;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 390px;
  }

  .u-section-12 .u-image-1 {
    width: 340px;
    height: 340px;
  }
}
.u-section-13 {
  min-height: 500px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

footer {
  background-color: #f8f9fc;
  padding: 40px 20px;
}

.footer-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  max-width: 1200px;
  margin: auto;
}

.footer-section {
  width: 22%;
  min-width: 250px;
  margin-bottom: 20px;
}

.footer-section h4 {
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
  color: black;
}

.footer-section p,
.footer-section ul li a {
  font-size: 14px;
  color: black;
  transition: 0.3s;
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a:hover {
  color: #007bff;
}

.company-info img {
  width: 50px;
  margin-bottom: 10px;
}

.company-info h3 {
  font-size: 18px;
  color: black;
}

.company-info p {
  font-size: 13px;
  color: black;
}

.social-icons {
  margin-top: 10px;
}

.social-icons a {
  font-size: 18px;
  color: #007bff;
  margin-right: 12px;
  transition: 0.3s;
  display: inline-block;
}

.social-icons a:hover {
  color: black;
  transform: scale(1.1);
}

.contact p {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.contact i {
  color: #007bff;
  font-size: 16px;
}

.footer-bottom {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid #ddd;
  margin-top: 20px;
}

.footer-bottom p {
  font-size: 13px;
  color: black;
}

.policy-links a {
  text-decoration: none;
  color: #007bff;
  font-size: 13px;
}

.policy-links a:hover {
  text-decoration: underline;
  color: black;
}
