// Romanian (ro)
plupload.addI18n({"%d files queued":"%d fișiere listate","%s already present in the queue.":"%s există deja în lista de așteptare.","%s specified, but cannot be found.":"","Add Files":"Adaugă fișiere","Add files to the upload queue and click the start button.":"Adaugă fișiere în lista apoi apasă butonul \"Începe încărcarea\".","b":"b","Close":"Închide","Drag files here.":"Trage aici fișierele.","Duplicate file error.":"Eroare duplicat fișier.","Error: File too large:":"Eroare: Fișierul este prea mare:","Error: Invalid file extension:":"Eroare: Extensia fișierului este invalidă:","File count error.":"Eroare numărare fișiere.","File extension error.":"Eroare extensie fișier.","File size error.":"Eroare dimensiune fișier.","File: %s":"Fișier: %s","File: %s, size: %d, max file size: %d":"Fișier: %s, mărime: %d, mărime maximă: %d","Filename":"Nume fișier","gb":"gb","HTTP Error.":"Eroare HTTP","Image format either wrong or not supported.":"Formatul de imagine ori este greșit ori nu este suportat.","Init error.":"Eroare inițializare.","kb":"kb","List":"","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Runtime ran out of available memory.","Select files":"Selectează fișierele","Size":"Mărime","Start Upload":"Începe încărcarea","Status":"Stare","Stop Upload":"Oprește încărcarea","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","Uploaded %d/%d files":"Fișiere încărcate %d/%d","You must specify either browse_button or drop_element.":""});