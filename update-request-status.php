<?php
// Start session to get driver ID
session_start();

// Database connection
require_once 'panel-admin-main1/db.php';

// Set headers to handle AJAX requests
header('Content-Type: application/json');

// Check if driver is logged in
if (!isset($_SESSION['driver_logged_in']) || $_SESSION['driver_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => 'You must be logged in to perform this action']);
    exit;
}

// Get driver ID from session
$driverId = $_SESSION['driver_id'] ?? null;

if (!$driverId) {
    echo json_encode(['success' => false, 'message' => 'Driver ID not found in session']);
    exit;
}

// Check if it's a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get request data
$data = json_decode(file_get_contents('php://input'), true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => 'Invalid request data']);
    exit;
}

// Validate required fields
if (empty($data['request_id']) || empty($data['status'])) {
    echo json_encode(['success' => false, 'message' => 'Missing required fields']);
    exit;
}

$requestId = $data['request_id'];
$status = $data['status'];

// Validate status
if (!in_array($status, ['accepted', 'rejected'])) {
    echo json_encode(['success' => false, 'message' => 'Invalid status']);
    exit;
}

try {
    // Begin transaction
    $conn->begin_transaction();
    
    // First check if the request is still pending
    $checkStmt = $conn->prepare("SELECT status FROM parcel_requests WHERE id = ?");
    $checkStmt->bind_param("i", $requestId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    
    if ($result->num_rows === 0) {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'Request not found']);
        exit;
    }
    
    $row = $result->fetch_assoc();
    if ($row['status'] !== 'pending') {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'This request has already been ' . $row['status']]);
        exit;
    }
    
    // Update the request status
    if ($status === 'accepted') {
        $stmt = $conn->prepare("UPDATE parcel_requests SET status = ?, assigned_driver_id = ? WHERE id = ?");
        $stmt->bind_param("sii", $status, $driverId, $requestId);
    } else {
        $stmt = $conn->prepare("UPDATE parcel_requests SET status = ? WHERE id = ?");
        $stmt->bind_param("si", $status, $requestId);
    }
    
    $result = $stmt->execute();
    
    if ($result) {
        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Request ' . $status . ' successfully']);
    } else {
        $conn->rollback();
        echo json_encode(['success' => false, 'message' => 'Failed to update request: ' . $stmt->error]);
    }
} catch (Exception $e) {
    $conn->rollback();
    echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}
?>
