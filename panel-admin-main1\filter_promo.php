<?php
session_start();
if (!isset($_SESSION['admin_logged_in'])) {
    header("Location: ../admin-login.php");
    exit();
}
?>

<?php
require_once "db.php"; // Database connection

$whereClauses = [];
$values = [];
$types = ""; // Store types for bind_param()

// Capture filter values and prepare SQL query
if (!empty($_POST['promocode'])) {
    $whereClauses[] = "promocode LIKE ?";
    $values[] = "%" . $_POST['promocode'] . "%";
    $types .= "s";
}
if (!empty($_POST['status'])) {
    $whereClauses[] = "status LIKE ?";
    $values[] = "%" . $_POST['status'] . "%";
    $types .= "s";
}


// Build SQL query
$sql = "SELECT * FROM promocode";
if (!empty($whereClauses)) {
    $sql .= " WHERE " . implode(" AND ", $whereClauses);
}

// Debug SQL Query (Optional: Uncomment to test)
// echo "SQL Query: " . $sql;
// print_r($values);

$stmt = $conn->prepare($sql);

if (!empty($values)) {
    $stmt->bind_param($types, ...$values);
}

$stmt->execute();
$result = $stmt->get_result();

$output = "";


?>
<?php $sr = 1; while ($row = $result->fetch_assoc()): ?>
    <tr>
        <td><?= $sr++; ?></td>
        <td><?= $row['promocode']; ?></td>
        <td><?= $row['start_date']; ?></td>
        <td><?= $row['expiry_date']; ?></td>
        <td><?= $row['short_description']; ?></td>
        <td><?= $row['status']; ?></td>
        <td>
            <select class="form-select action-select" data-id="<?= $row['promocode_id']; ?>" data-code="<?= $row['promocode']; ?>" data-start="<?= $row['start_date']; ?>" data-expiry="<?= $row['expiry_date']; ?>" data-description="<?= $row['short_description']; ?>" data-status="<?= $row['status']; ?>">
                <option value="">Select</option>
                <option value="edit">Edit</option>
                <option value="delete">Delete</option>
            </select>
        </td>
    </tr>
<?php endwhile; ?>
<?php
while ($row = $result->fetch_assoc()) {
    $output .= "<tr>
        <td>{$row['user_id']}</td>
        <td>{$row['user_name']}</td>
        <td>{$row['user_mobile']}</td>
        <td>{$row['user_email']}</td>
        <td>{$row['user_gender']}</td>
        <td>{$row['user_address']}</td>
        <td>{$row['user_city']}</td>
        <td>{$row['user_state']}</td>
        <td>
            <div class='dropdown'>
                <button class='btn btn-sm btn-white dropdown-toggle' type='button' data-bs-toggle='dropdown'>
                    Action
                </button>
                <ul class='dropdown-menu'>
                    <li><a class='dropdown-item' data-bs-toggle='modal' data-bs-target='#editUserModal'>Edit</a></li>
                    <li><a class='dropdown-item text-danger delete-user-btn' href='javascript:void(0);'>Delete</a></li>
                </ul>
            </div>
        </td>
    </tr>";
}

// Debug the number of rows fetched
if ($result->num_rows === 0) {
    $output = "<tr><td colspan='9' class='text-center'>No results found</td></tr>";
}

echo $output;

$stmt->close();
$conn->close();
?>
