// Hebrew (he)
plupload.addI18n({"%d files queued":"%d קבצים נותרו","%s already present in the queue.":"%sקובץ נמצא כבר ברשימת הקבצים.","%s specified, but cannot be found.":"%s צויין, אך לא נמצא.","Add Files":"הוסף קבצים","Add files to the upload queue and click the start button.":"הוסף קבצים לרשימה ולחץ על כפתור שליחה להתחלת פעולות העלאה","b":"B","Close":"סגור","Drag files here.":"גרור קבצים לכאן","Duplicate file error.":"קובץ כפול","Error: File too large:":"שגיאה: קובץ חורג מהגודל המותר:","Error: Invalid file extension:":"שגיאה: סוג קובץ לא נתמך:","File count error.":"שגיאת מספר קבצים","File extension error.":"קובץ זה לא נתמך","File size error.":"גודל קובץ חורג מהמותר","File: %s":"קובץ: %s","File: %s, size: %d, max file size: %d":"קובץ: %s, גודל: %d, גודל מקסימלי: %d","Filename":"שם קובץ","gb":"GB","HTTP Error.":"שגיאת פרוטוקול","Image format either wrong or not supported.":"תמונה פגומה או סוג תמונה לא נתמך","Init error.":"שגיאת איתחול","kb":"KB","List":"רשימה","mb":"MB","N/A":"שגיאה","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"הרזולוציה גבוהה מדי!  <b>%s</b> הפלטפורמה תומכת בתמונות עד גודל px.","Runtime ran out of available memory.":"שגיאת מחסור בזיכרון","Select files":"בחר קבצים","Size":"גודל","Start Upload":"שליחה","Status":"אחוז","Stop Upload":"בטל העלאה","tb":"tb","Thumbnails":"תמונות ממוזערות","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"אלמנטי ההעלאה מקבלים רק %d קובץ(ים) בפעם אחת. קבצים נוספים הוסרו.","Upload URL might be wrong or doesn't exist.":"כתובת URL שגויה או לא קיימת.","Uploaded %d/%d files":"מעלה: %d/%d","You must specify either browse_button or drop_element.":"יש לציין או browse_button או drop_element."});