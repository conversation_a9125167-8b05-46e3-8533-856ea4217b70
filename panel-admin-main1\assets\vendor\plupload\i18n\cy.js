// Welsh (cy)
plupload.addI18n({"%d files queued":"%d ffeil mewn ciw","%s already present in the queue.":"%s yn y ciw yn barod.","%s specified, but cannot be found.":"","Add Files":"Ychwanegu Ffeiliau","Add files to the upload queue and click the start button.":"Ychwanegwch ffeiliau i'r ciw lanlwytho a chlicio'r botwm dechrau.","b":"b","Close":"Cau","Drag files here.":"Llusgwch ffeiliau yma.","Duplicate file error.":"Gwall ffeil ddyblyg.","Error: File too large:":"Gwall: Ffeil yn rhy fawr:","Error: Invalid file extension:":"Gwall: estyniad ffeil annilys:","File count error.":"Gwall cyfri ffeiliau.","File extension error.":"Gwall estyniad ffeil.","File size error.":"Gwall maint ffeil.","File: %s":"Ffeil: %s","File: %s, size: %d, max file size: %d":"Ffeil: %s, maint: %d, maint mwyaf ffeil: %d","Filename":"Enw'r ffeil","gb":"gb","HTTP Error.":"Gwall HTTP.","Image format either wrong or not supported.":"Fformat delwedd yn anghywir neu heb ei gynnal.","Init error.":"Gwall cych.","kb":"kb","List":"","mb":"mb","N/A":"Dd/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"","Runtime ran out of available memory.":"Allan o gof.","Select files":"Dewis ffeiliau","Size":"Maint","Start Upload":"Dechrau Lanlwytho","Status":"Statws","Stop Upload":"Atal Lanlwytho","tb":"tb","Thumbnails":"","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Mae'r elfen lanlwytho yn derbyn %d ffeil ar y tro. Caiff ffeiliau ychwanegol eu tynnu.","Upload URL might be wrong or doesn't exist.":"URL y lanlwythiad ynb anghywir neu ddim yn bodoli.","Uploaded %d/%d files":"Lanlwythwyd  %d/%d ffeil","You must specify either browse_button or drop_element.":""});