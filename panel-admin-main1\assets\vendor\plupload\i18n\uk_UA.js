// Ukrainian (Ukraine) (uk_UA)
plupload.addI18n({"%d files queued":"В черзі %d файл(ів)","%s already present in the queue.":"%s вже присутній у черзі.","%s specified, but cannot be found.":"%s вказано, але не може бути знайдено.","Add Files":"Додати файли","Add files to the upload queue and click the start button.":"Додайте файли в чергу та натисніть кнопку \"Завантажити файли\".","b":"б","Close":"Закрити","Drag files here.":"Перетягніть файли сюди.","Duplicate file error.":"Такий файл вже присутній в черзі.","Error: File too large:":"Помилка: Файл занадто великий:","Error: Invalid file extension:":"Помилка: У файлу неправильне розширення:","File count error.":"Занадто багато файлів.","File extension error.":"Неправильне розширення файлу.","File size error.":"Неправильний розмір файлу.","File: %s":"Файл: %s","File: %s, size: %d, max file size: %d":"Файл:  %s, розмір: %d, макс. розмір файлу: %d","Filename":"Назва файлу","gb":"гб","HTTP Error.":"Помилка HTTP.","Image format either wrong or not supported.":"Формат картинки не правильний або не підтримується.","Init error.":"Помилка ініціалізації.","kb":"кб","List":"Список","mb":"мб","N/A":"Н/Д","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Роздільна здатність поза межами! Робоче середовище <b>%s</b> підтримує зображення лише до %wx%hpx.","Runtime ran out of available memory.":"Робоче середовище перевищило ліміт доступної пам'яті.","Select files":"Оберіть файли","Size":"Розмір","Start Upload":"Почати завантаження","Status":"Статус","Stop Upload":"Зупинити завантаження","tb":"тб","Thumbnails":"Мініатюри","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Завантажувальний елемент приймає лише %d файл(ів) одночасно. Зайві файли було відкинуто.","Upload URL might be wrong or doesn't exist.":"Адреса завантаження неправильна або не існує.","Uploaded %d/%d files":"Завантажено %d/%d файлів","You must specify either browse_button or drop_element.":"Ви маєте вказати або browse_button, або drop_element."});