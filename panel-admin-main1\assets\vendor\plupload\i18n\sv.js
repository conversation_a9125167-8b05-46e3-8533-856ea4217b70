// Swedish (sv)
plupload.addI18n({"%d files queued":"%d filer i kö","%s already present in the queue.":"%s är redan tillagd.","%s specified, but cannot be found.":"%s specificerad, men hittades inte.","Add Files":"Lägg till","Add files to the upload queue and click the start button.":"Lägg till filer till kön och tryck på start.","b":"b","Close":"Stäng","Drag files here.":"Dra filer hit","Duplicate file error.":"Problem med dubbla filer.","Error: File too large:":"Fel: Filen är för stor:","Error: Invalid file extension:":"Fel: Ej godkänd filändelse.","File count error.":"Räknefel.","File extension error.":"Problem med filändelse.","File size error.":"Problem med filstorlek.","File: %s":"Fil: %s","File: %s, size: %d, max file size: %d":"Fil: %s, storlek: %d, max storlek: %d","Filename":"Filnamn","gb":"gb","HTTP Error.":"HTTP problem.","Image format either wrong or not supported.":"Bildformatet är fel eller så finns inte stöd för det.","Init error.":"Problem vid initialisering.","kb":"kb","List":"Lista","mb":"mb","N/A":"N/A","Resoultion out of boundaries! <b>%s</b> runtime supports images only up to %wx%hpx.":"Upplösning utanför gränserna! <b>%s</b> bara bilder upp till %wx%hpx stöds.","Runtime ran out of available memory.":"Slut på minne.","Select files":"Välj filer","Size":"Storlek","Start Upload":"Starta","Status":"Status","Stop Upload":"Avbryt","tb":"tb","Thumbnails":"Miniatyrer","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Det går bara lägga till %d filer åt gången, allt utöver detta togs bort.","Upload URL might be wrong or doesn't exist.":"URL:en va fel eller existerar inte.","Uploaded %d/%d files":"Laddade upp %d/%d filer","You must specify either browse_button or drop_element.":"Du behöver specificera browse_button eller drop_element."});